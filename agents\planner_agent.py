"""
Planner Agent for Jarvis AI Assistant
Breaks down complex tasks into manageable steps
"""

import json
import re
from typing import Dict, List, Optional, Any
from .base_agent import BaseAgent, AgentResult
from core.llm import llm_manager


class PlannerAgent(BaseAgent):
    """Agent responsible for task planning and decomposition"""
    
    def __init__(self):
        super().__init__(
            name="planner",
            description="Breaks down complex tasks into manageable steps and coordinates execution"
        )
        self.capabilities = [
            "task_decomposition",
            "step_planning", 
            "dependency_analysis",
            "resource_estimation",
            "execution_coordination"
        ]
    
    def can_handle(self, task: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if this is a planning task"""
        planning_keywords = [
            "plan", "break down", "steps", "organize", "coordinate",
            "strategy", "approach", "decompose", "structure"
        ]
        
        task_lower = task.lower()
        return any(keyword in task_lower for keyword in planning_keywords)
    
    async def execute(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> AgentResult:
        """Execute planning task"""
        try:
            self.logger.info(f"Planning task: {task}")
            
            # Determine if this is a simple or complex task
            complexity = await self._assess_complexity(task, context)
            
            if complexity == "simple":
                plan = await self._create_simple_plan(task, context)
            else:
                plan = await self._create_detailed_plan(task, context)
            
            return AgentResult(
                success=True,
                data=plan,
                message=f"Created {complexity} plan with {len(plan.get('steps', []))} steps"
            )
            
        except Exception as e:
            self.logger.error(f"Planning failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to create plan"
            )
    
    async def _assess_complexity(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Assess task complexity"""
        complexity_prompt = f"""
        Analyze the complexity of this task:
        
        Task: {task}
        Context: {json.dumps(context or {}, indent=2)}
        
        Consider:
        - Number of steps required
        - Dependencies between steps
        - Required tools/resources
        - Potential complications
        - Time estimation
        
        Respond with either "simple" or "complex":
        - Simple: Can be completed in 1-3 straightforward steps
        - Complex: Requires multiple steps, coordination, or specialized tools
        
        Response format: Just the word "simple" or "complex"
        """
        
        try:
            response = await llm_manager.generate(
                prompt=complexity_prompt,
                temperature=0.1
            )
            
            response = response.strip().lower()
            return "complex" if "complex" in response else "simple"
            
        except Exception as e:
            self.logger.warning(f"Complexity assessment failed: {e}")
            return "simple"
    
    async def _create_simple_plan(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a simple plan for straightforward tasks"""
        simple_plan_prompt = f"""
        Create a simple execution plan for this task:
        
        Task: {task}
        Context: {json.dumps(context or {}, indent=2)}
        
        Create a JSON plan with:
        {{
            "objective": "clear objective statement",
            "steps": [
                {{
                    "id": 1,
                    "description": "step description",
                    "agent": "suggested agent name",
                    "estimated_time": "time estimate",
                    "dependencies": []
                }}
            ],
            "success_criteria": "how to measure success",
            "estimated_total_time": "total time estimate"
        }}
        
        Keep it simple with 1-3 steps maximum.
        """
        
        response = await llm_manager.generate(
            prompt=simple_plan_prompt,
            temperature=0.3
        )
        
        try:
            return json.loads(response)
        except json.JSONDecodeError:
            # Fallback simple plan
            return {
                "objective": task,
                "steps": [
                    {
                        "id": 1,
                        "description": task,
                        "agent": "general",
                        "estimated_time": "5 minutes",
                        "dependencies": []
                    }
                ],
                "success_criteria": "Task completed successfully",
                "estimated_total_time": "5 minutes"
            }
    
    async def _create_detailed_plan(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a detailed plan for complex tasks"""
        detailed_plan_prompt = f"""
        Create a comprehensive execution plan for this complex task:
        
        Task: {task}
        Context: {json.dumps(context or {}, indent=2)}
        
        Available agents:
        - planner: Task planning and coordination
        - developer: Code writing and development
        - shell: Command line operations
        - file_manager: File operations
        - browser: Web browsing and research
        - system_control: System operations
        - research: Information gathering
        
        Create a detailed JSON plan with:
        {{
            "objective": "clear objective statement",
            "phases": [
                {{
                    "name": "phase name",
                    "description": "phase description",
                    "steps": [
                        {{
                            "id": 1,
                            "description": "detailed step description",
                            "agent": "agent name",
                            "estimated_time": "time estimate",
                            "dependencies": [list of step IDs this depends on],
                            "inputs": ["required inputs"],
                            "outputs": ["expected outputs"],
                            "success_criteria": "step success criteria"
                        }}
                    ]
                }}
            ],
            "resources_needed": ["list of required resources"],
            "potential_risks": ["list of potential risks"],
            "contingency_plans": ["backup plans"],
            "success_criteria": "overall success criteria",
            "estimated_total_time": "total time estimate"
        }}
        
        Be thorough and consider dependencies, error handling, and validation steps.
        """
        
        response = await llm_manager.generate(
            prompt=detailed_plan_prompt,
            temperature=0.3
        )
        
        try:
            plan = json.loads(response)
            
            # Validate and enhance the plan
            plan = await self._validate_and_enhance_plan(plan)
            
            return plan
            
        except json.JSONDecodeError:
            self.logger.warning("Failed to parse detailed plan JSON")
            # Create a fallback plan
            return await self._create_simple_plan(task, context)
    
    async def _validate_and_enhance_plan(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and enhance the generated plan"""
        try:
            # Ensure required fields exist
            if "objective" not in plan:
                plan["objective"] = "Complete the requested task"
            
            if "phases" not in plan:
                plan["phases"] = []
            
            # Add step IDs if missing and validate dependencies
            step_id = 1
            all_step_ids = set()
            
            for phase in plan["phases"]:
                if "steps" not in phase:
                    phase["steps"] = []
                
                for step in phase["steps"]:
                    if "id" not in step:
                        step["id"] = step_id
                    
                    all_step_ids.add(step["id"])
                    step_id += 1
                    
                    # Ensure required step fields
                    if "description" not in step:
                        step["description"] = "Execute step"
                    if "agent" not in step:
                        step["agent"] = "general"
                    if "estimated_time" not in step:
                        step["estimated_time"] = "10 minutes"
                    if "dependencies" not in step:
                        step["dependencies"] = []
                    if "inputs" not in step:
                        step["inputs"] = []
                    if "outputs" not in step:
                        step["outputs"] = []
                    if "success_criteria" not in step:
                        step["success_criteria"] = "Step completed without errors"
            
            # Validate dependencies
            for phase in plan["phases"]:
                for step in phase["steps"]:
                    valid_deps = [dep for dep in step["dependencies"] if dep in all_step_ids]
                    step["dependencies"] = valid_deps
            
            # Add missing top-level fields
            if "resources_needed" not in plan:
                plan["resources_needed"] = []
            if "potential_risks" not in plan:
                plan["potential_risks"] = []
            if "contingency_plans" not in plan:
                plan["contingency_plans"] = []
            if "success_criteria" not in plan:
                plan["success_criteria"] = "All steps completed successfully"
            if "estimated_total_time" not in plan:
                plan["estimated_total_time"] = "1 hour"
            
            return plan
            
        except Exception as e:
            self.logger.error(f"Plan validation failed: {e}")
            return plan
    
    async def create_execution_order(self, plan: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create optimal execution order considering dependencies"""
        try:
            all_steps = []
            
            # Collect all steps from all phases
            for phase in plan.get("phases", []):
                for step in phase.get("steps", []):
                    all_steps.append({
                        **step,
                        "phase": phase.get("name", "Unknown")
                    })
            
            # Sort by dependencies (topological sort)
            execution_order = []
            completed_steps = set()
            
            while len(execution_order) < len(all_steps):
                progress_made = False
                
                for step in all_steps:
                    if step["id"] in completed_steps:
                        continue
                    
                    # Check if all dependencies are completed
                    deps_completed = all(
                        dep in completed_steps 
                        for dep in step.get("dependencies", [])
                    )
                    
                    if deps_completed:
                        execution_order.append(step)
                        completed_steps.add(step["id"])
                        progress_made = True
                
                if not progress_made:
                    # Handle circular dependencies or missing dependencies
                    remaining_steps = [
                        step for step in all_steps 
                        if step["id"] not in completed_steps
                    ]
                    execution_order.extend(remaining_steps)
                    break
            
            return execution_order
            
        except Exception as e:
            self.logger.error(f"Failed to create execution order: {e}")
            return []
    
    async def estimate_resources(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate resources needed for plan execution"""
        try:
            total_time = 0
            agents_needed = set()
            tools_needed = set()
            
            for phase in plan.get("phases", []):
                for step in phase.get("steps", []):
                    # Parse time estimate
                    time_str = step.get("estimated_time", "10 minutes")
                    time_minutes = self._parse_time_to_minutes(time_str)
                    total_time += time_minutes
                    
                    # Collect agents and tools
                    agents_needed.add(step.get("agent", "general"))
                    tools_needed.update(step.get("inputs", []))
            
            return {
                "estimated_total_time_minutes": total_time,
                "estimated_total_time_formatted": self._format_minutes_to_time(total_time),
                "agents_needed": list(agents_needed),
                "tools_needed": list(tools_needed),
                "complexity_score": min(10, len(plan.get("phases", [])) * 2 + total_time / 60)
            }
            
        except Exception as e:
            self.logger.error(f"Resource estimation failed: {e}")
            return {
                "estimated_total_time_minutes": 60,
                "estimated_total_time_formatted": "1 hour",
                "agents_needed": ["general"],
                "tools_needed": [],
                "complexity_score": 5
            }
    
    def _parse_time_to_minutes(self, time_str: str) -> int:
        """Parse time string to minutes"""
        try:
            time_str = time_str.lower()
            
            if "hour" in time_str:
                hours = re.findall(r'(\d+)', time_str)
                return int(hours[0]) * 60 if hours else 60
            elif "minute" in time_str:
                minutes = re.findall(r'(\d+)', time_str)
                return int(minutes[0]) if minutes else 10
            else:
                # Try to extract any number
                numbers = re.findall(r'(\d+)', time_str)
                return int(numbers[0]) if numbers else 10
                
        except Exception:
            return 10
    
    def _format_minutes_to_time(self, minutes: int) -> str:
        """Format minutes to human readable time"""
        if minutes < 60:
            return f"{minutes} minutes"
        else:
            hours = minutes // 60
            remaining_minutes = minutes % 60
            if remaining_minutes == 0:
                return f"{hours} hour{'s' if hours > 1 else ''}"
            else:
                return f"{hours} hour{'s' if hours > 1 else ''} {remaining_minutes} minutes"
