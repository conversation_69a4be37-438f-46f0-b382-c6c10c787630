"""
Browser Agent for Jarvis AI Assistant
Handles web browsing, scraping, and automation tasks
"""

import asyncio
import json
import re
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin, urlparse
import requests
from bs4 import BeautifulSoup
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>
from .base_agent import BaseAgent, AgentResult
from core.llm import llm_manager


class BrowserAgent(BaseAgent):
    """Agent responsible for web browsing and automation"""
    
    def __init__(self):
        super().__init__(
            name="browser",
            description="Handles web browsing, scraping, and browser automation tasks"
        )
        self.capabilities = [
            "web_scraping",
            "page_navigation",
            "form_filling",
            "element_interaction",
            "screenshot_capture",
            "content_extraction",
            "link_following"
        ]
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.session = requests.Session()
    
    def can_handle(self, task: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if this is a browser/web task"""
        web_keywords = [
            "browse", "website", "web", "url", "scrape", "download",
            "navigate", "click", "form", "submit", "screenshot",
            "search", "google", "extract", "crawl", "automation"
        ]
        
        task_lower = task.lower()
        return any(keyword in task_lower for keyword in web_keywords)
    
    async def execute(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> AgentResult:
        """Execute browser task"""
        try:
            self.logger.info(f"Executing browser task: {task}")
            
            # Determine the type of browser operation
            operation = await self._classify_operation(task, context)
            
            if operation == "scrape":
                result = await self._scrape_content(task, context)
            elif operation == "navigate":
                result = await self._navigate_page(task, context)
            elif operation == "interact":
                result = await self._interact_with_page(task, context)
            elif operation == "screenshot":
                result = await self._take_screenshot(task, context)
            elif operation == "search":
                result = await self._search_web(task, context)
            elif operation == "download":
                result = await self._download_content(task, context)
            else:
                result = await self._general_browser_operation(task, context)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Browser task failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="Browser task execution failed"
            )
        finally:
            # Clean up browser resources if needed
            if kwargs.get("cleanup", True):
                await self._cleanup_browser()
    
    async def _classify_operation(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Classify the type of browser operation"""
        task_lower = task.lower()
        
        if any(word in task_lower for word in ["scrape", "extract", "get content"]):
            return "scrape"
        elif any(word in task_lower for word in ["navigate", "go to", "visit", "open"]):
            return "navigate"
        elif any(word in task_lower for word in ["click", "fill", "submit", "interact"]):
            return "interact"
        elif any(word in task_lower for word in ["screenshot", "capture", "image"]):
            return "screenshot"
        elif any(word in task_lower for word in ["search", "google", "find"]):
            return "search"
        elif any(word in task_lower for word in ["download", "save", "fetch"]):
            return "download"
        else:
            return "general"
    
    async def _scrape_content(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Scrape content from web pages"""
        try:
            url = self._extract_url(task, context)
            
            if not url:
                return AgentResult(
                    success=False,
                    error="No URL specified",
                    message="Please specify a URL to scrape"
                )
            
            # Use requests for simple scraping
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract different types of content
            content = {
                "url": url,
                "title": soup.title.string if soup.title else "",
                "text": soup.get_text(strip=True),
                "links": [urljoin(url, a.get('href', '')) for a in soup.find_all('a', href=True)],
                "images": [urljoin(url, img.get('src', '')) for img in soup.find_all('img', src=True)],
                "headings": [h.get_text(strip=True) for h in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])],
                "meta_description": "",
                "status_code": response.status_code
            }
            
            # Extract meta description
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                content["meta_description"] = meta_desc.get('content', '')
            
            # Extract specific elements if requested
            if context and "selectors" in context:
                content["custom_elements"] = {}
                for selector_name, selector in context["selectors"].items():
                    elements = soup.select(selector)
                    content["custom_elements"][selector_name] = [
                        elem.get_text(strip=True) for elem in elements
                    ]
            
            return AgentResult(
                success=True,
                data=content,
                message=f"Successfully scraped content from: {url}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to scrape content"
            )
    
    async def _navigate_page(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Navigate to a web page using browser automation"""
        try:
            url = self._extract_url(task, context)
            
            if not url:
                return AgentResult(
                    success=False,
                    error="No URL specified",
                    message="Please specify a URL to navigate to"
                )
            
            # Initialize browser if needed
            if not self.browser:
                await self._init_browser()
            
            # Navigate to page
            await self.page.goto(url, wait_until="networkidle")
            
            # Get page info
            page_info = {
                "url": self.page.url,
                "title": await self.page.title(),
                "content": await self.page.content(),
                "viewport": await self.page.viewport_size()
            }
            
            return AgentResult(
                success=True,
                data=page_info,
                message=f"Successfully navigated to: {url}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to navigate to page"
            )
    
    async def _interact_with_page(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Interact with page elements"""
        try:
            if not self.page:
                return AgentResult(
                    success=False,
                    error="No active page",
                    message="Please navigate to a page first"
                )
            
            # Parse interaction instructions
            interactions = context.get("interactions", []) if context else []
            
            if not interactions:
                # Generate interactions from task description
                interactions = await self._generate_interactions(task)
            
            results = []
            for interaction in interactions:
                try:
                    result = await self._perform_interaction(interaction)
                    results.append(result)
                except Exception as e:
                    results.append({
                        "action": interaction.get("action", "unknown"),
                        "success": False,
                        "error": str(e)
                    })
            
            success = all(r.get("success", False) for r in results)
            
            return AgentResult(
                success=success,
                data={
                    "interactions": results,
                    "page_url": self.page.url,
                    "page_title": await self.page.title()
                },
                message=f"Performed {len(results)} interactions, {sum(1 for r in results if r.get('success'))} successful"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to interact with page"
            )
    
    async def _take_screenshot(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Take screenshot of current page"""
        try:
            if not self.page:
                # Navigate to URL if provided
                url = self._extract_url(task, context)
                if url:
                    await self._navigate_page(task, context)
                else:
                    return AgentResult(
                        success=False,
                        error="No active page or URL",
                        message="Please navigate to a page first or provide a URL"
                    )
            
            # Take screenshot
            screenshot_path = context.get("path", "screenshot.png") if context else "screenshot.png"
            
            await self.page.screenshot(
                path=screenshot_path,
                full_page=context.get("full_page", True) if context else True
            )
            
            return AgentResult(
                success=True,
                data={
                    "screenshot_path": screenshot_path,
                    "page_url": self.page.url,
                    "page_title": await self.page.title()
                },
                message=f"Screenshot saved to: {screenshot_path}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to take screenshot"
            )
    
    async def _search_web(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Search the web for information"""
        try:
            query = context.get("query") if context else None
            
            if not query:
                # Extract query from task
                query = await self._extract_search_query(task)
            
            if not query:
                return AgentResult(
                    success=False,
                    error="No search query specified",
                    message="Please specify a search query"
                )
            
            # Use Google search
            search_url = f"https://www.google.com/search?q={query}"
            
            # Scrape search results
            response = self.session.get(search_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract search results
            results = []
            for result_div in soup.find_all('div', class_='g'):
                title_elem = result_div.find('h3')
                link_elem = result_div.find('a')
                snippet_elem = result_div.find('span', class_='aCOpRe')
                
                if title_elem and link_elem:
                    results.append({
                        "title": title_elem.get_text(strip=True),
                        "url": link_elem.get('href', ''),
                        "snippet": snippet_elem.get_text(strip=True) if snippet_elem else ""
                    })
            
            return AgentResult(
                success=True,
                data={
                    "query": query,
                    "results": results[:10],  # Limit to top 10 results
                    "total_found": len(results)
                },
                message=f"Found {len(results)} search results for: {query}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to search web"
            )
    
    async def _download_content(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Download content from URL"""
        try:
            url = self._extract_url(task, context)
            
            if not url:
                return AgentResult(
                    success=False,
                    error="No URL specified",
                    message="Please specify a URL to download from"
                )
            
            # Download content
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()
            
            # Determine filename
            filename = context.get("filename") if context else None
            if not filename:
                filename = url.split('/')[-1] or "downloaded_content"
            
            # Save content
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            return AgentResult(
                success=True,
                data={
                    "url": url,
                    "filename": filename,
                    "size": len(response.content),
                    "content_type": response.headers.get('content-type', 'unknown')
                },
                message=f"Downloaded content to: {filename}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to download content"
            )
    
    async def _general_browser_operation(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Handle general browser operations"""
        general_prompt = f"""
        You are a web automation expert. Help with this browser task:
        
        Task: {task}
        Context: {context or {}}
        
        Provide specific instructions for the browser operation, including:
        1. What operation needs to be performed
        2. Required parameters (URLs, selectors, etc.)
        3. Step-by-step instructions
        4. Expected outcomes
        """
        
        try:
            response = await llm_manager.generate(
                prompt=general_prompt,
                temperature=0.3
            )
            
            return AgentResult(
                success=True,
                data={"instructions": response},
                message="Generated browser operation instructions"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to process general browser operation"
            )
    
    async def _init_browser(self):
        """Initialize browser instance"""
        try:
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(headless=True)
            self.page = await self.browser.new_page()
            
            # Set user agent
            await self.page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
        except Exception as e:
            self.logger.error(f"Failed to initialize browser: {e}")
            raise
    
    async def _cleanup_browser(self):
        """Clean up browser resources"""
        try:
            if self.page:
                await self.page.close()
                self.page = None
            
            if self.browser:
                await self.browser.close()
                self.browser = None
                
        except Exception as e:
            self.logger.warning(f"Browser cleanup failed: {e}")
    
    def _extract_url(self, task: str, context: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """Extract URL from task or context"""
        if context and "url" in context:
            return context["url"]
        
        # Look for URLs in task text
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        urls = re.findall(url_pattern, task)
        
        return urls[0] if urls else None
    
    async def _extract_search_query(self, task: str) -> Optional[str]:
        """Extract search query from task"""
        # Remove common prefixes
        query = task.lower()
        prefixes = ["search for", "find", "look for", "google", "search"]
        
        for prefix in prefixes:
            if query.startswith(prefix):
                query = query[len(prefix):].strip()
                break
        
        return query if query else None
    
    async def _generate_interactions(self, task: str) -> List[Dict[str, Any]]:
        """Generate interaction instructions from task description"""
        interaction_prompt = f"""
        Generate browser interaction instructions for this task:
        
        Task: {task}
        
        Respond with a JSON array of interactions:
        [
            {{
                "action": "click|fill|select|wait",
                "selector": "CSS selector",
                "value": "value to enter (for fill/select)",
                "description": "what this interaction does"
            }}
        ]
        
        Available actions:
        - click: Click an element
        - fill: Fill a form field
        - select: Select from dropdown
        - wait: Wait for element to appear
        """
        
        try:
            response = await llm_manager.generate(
                prompt=interaction_prompt,
                temperature=0.2
            )
            
            interactions = json.loads(response)
            return interactions if isinstance(interactions, list) else []
            
        except Exception as e:
            self.logger.error(f"Failed to generate interactions: {e}")
            return []
    
    async def _perform_interaction(self, interaction: Dict[str, Any]) -> Dict[str, Any]:
        """Perform a single interaction"""
        action = interaction.get("action")
        selector = interaction.get("selector")
        value = interaction.get("value")
        
        try:
            if action == "click":
                await self.page.click(selector)
            elif action == "fill":
                await self.page.fill(selector, value)
            elif action == "select":
                await self.page.select_option(selector, value)
            elif action == "wait":
                await self.page.wait_for_selector(selector)
            else:
                raise ValueError(f"Unknown action: {action}")
            
            return {
                "action": action,
                "selector": selector,
                "value": value,
                "success": True
            }
            
        except Exception as e:
            return {
                "action": action,
                "selector": selector,
                "value": value,
                "success": False,
                "error": str(e)
            }
