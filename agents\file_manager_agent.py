"""
File Manager Agent for Jarvis AI Assistant
Handles file and directory operations
"""

import os
import shutil
import json
import mimetypes
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from .base_agent import BaseAgent, AgentResult
from core.llm import llm_manager


class FileManagerAgent(BaseAgent):
    """Agent responsible for file and directory operations"""
    
    def __init__(self):
        super().__init__(
            name="file_manager",
            description="Manages files and directories, handles file operations safely"
        )
        self.capabilities = [
            "file_reading",
            "file_writing",
            "file_copying",
            "file_moving",
            "file_deletion",
            "directory_operations",
            "file_search",
            "file_analysis"
        ]
        self.working_directory = os.getcwd()
    
    def can_handle(self, task: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if this is a file management task"""
        file_keywords = [
            "file", "folder", "directory", "read", "write", "copy", "move",
            "delete", "create", "save", "load", "search", "find", "list",
            "organize", "backup", "archive", "extract", "compress"
        ]
        
        task_lower = task.lower()
        return any(keyword in task_lower for keyword in file_keywords)
    
    async def execute(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> AgentResult:
        """Execute file management task"""
        try:
            self.logger.info(f"Executing file management task: {task}")
            
            # Determine the type of file operation
            operation = await self._classify_operation(task, context)
            
            if operation == "read":
                result = await self._read_file(task, context)
            elif operation == "write":
                result = await self._write_file(task, context)
            elif operation == "copy":
                result = await self._copy_file(task, context)
            elif operation == "move":
                result = await self._move_file(task, context)
            elif operation == "delete":
                result = await self._delete_file(task, context)
            elif operation == "create_directory":
                result = await self._create_directory(task, context)
            elif operation == "list":
                result = await self._list_directory(task, context)
            elif operation == "search":
                result = await self._search_files(task, context)
            elif operation == "analyze":
                result = await self._analyze_file(task, context)
            else:
                result = await self._general_file_operation(task, context)
            
            return result
            
        except Exception as e:
            self.logger.error(f"File management task failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="File management task execution failed"
            )
    
    async def _classify_operation(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Classify the type of file operation"""
        task_lower = task.lower()
        
        # Simple keyword-based classification
        if any(word in task_lower for word in ["read", "open", "view", "show", "display"]):
            return "read"
        elif any(word in task_lower for word in ["write", "save", "create file", "edit"]):
            return "write"
        elif any(word in task_lower for word in ["copy", "duplicate"]):
            return "copy"
        elif any(word in task_lower for word in ["move", "rename", "relocate"]):
            return "move"
        elif any(word in task_lower for word in ["delete", "remove", "rm"]):
            return "delete"
        elif any(word in task_lower for word in ["mkdir", "create directory", "create folder"]):
            return "create_directory"
        elif any(word in task_lower for word in ["list", "ls", "dir", "show files"]):
            return "list"
        elif any(word in task_lower for word in ["search", "find", "locate"]):
            return "search"
        elif any(word in task_lower for word in ["analyze", "examine", "inspect"]):
            return "analyze"
        else:
            return "general"
    
    async def _read_file(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Read file content"""
        try:
            # Extract file path from task or context
            file_path = self._extract_file_path(task, context)
            
            if not file_path:
                return AgentResult(
                    success=False,
                    error="No file path specified",
                    message="Please specify a file path to read"
                )
            
            file_path = self._resolve_path(file_path)
            
            if not os.path.exists(file_path):
                return AgentResult(
                    success=False,
                    error="File not found",
                    message=f"File does not exist: {file_path}"
                )
            
            if not os.path.isfile(file_path):
                return AgentResult(
                    success=False,
                    error="Not a file",
                    message=f"Path is not a file: {file_path}"
                )
            
            # Check file size
            file_size = os.path.getsize(file_path)
            if file_size > 10 * 1024 * 1024:  # 10MB limit
                return AgentResult(
                    success=False,
                    error="File too large",
                    message=f"File is too large to read: {file_size} bytes"
                )
            
            # Determine file type and read accordingly
            mime_type, _ = mimetypes.guess_type(file_path)
            
            if mime_type and mime_type.startswith('text'):
                # Text file
                with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                    content = f.read()
            elif file_path.endswith(('.json', '.yaml', '.yml', '.xml')):
                # Structured text files
                with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                    content = f.read()
            else:
                # Binary file - read as bytes and encode
                with open(file_path, 'rb') as f:
                    binary_content = f.read()
                content = f"Binary file ({len(binary_content)} bytes)"
            
            file_info = {
                "path": file_path,
                "size": file_size,
                "mime_type": mime_type,
                "modified": datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat(),
                "content": content
            }
            
            return AgentResult(
                success=True,
                data=file_info,
                message=f"Successfully read file: {file_path}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to read file"
            )
    
    async def _write_file(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Write content to file"""
        try:
            file_path = self._extract_file_path(task, context)
            content = context.get("content", "") if context else ""
            
            if not file_path:
                return AgentResult(
                    success=False,
                    error="No file path specified",
                    message="Please specify a file path to write to"
                )
            
            if not content and "content" not in (context or {}):
                # Generate content using LLM
                content = await self._generate_file_content(task, file_path)
            
            file_path = self._resolve_path(file_path)
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # Write file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            file_info = {
                "path": file_path,
                "size": len(content.encode('utf-8')),
                "content_preview": content[:200] + "..." if len(content) > 200 else content
            }
            
            return AgentResult(
                success=True,
                data=file_info,
                message=f"Successfully wrote file: {file_path}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to write file"
            )
    
    async def _copy_file(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Copy file or directory"""
        try:
            source_path = context.get("source") if context else None
            dest_path = context.get("destination") if context else None
            
            if not source_path or not dest_path:
                # Extract paths from task
                paths = await self._extract_source_dest_paths(task)
                source_path = source_path or paths.get("source")
                dest_path = dest_path or paths.get("destination")
            
            if not source_path or not dest_path:
                return AgentResult(
                    success=False,
                    error="Missing source or destination path",
                    message="Please specify both source and destination paths"
                )
            
            source_path = self._resolve_path(source_path)
            dest_path = self._resolve_path(dest_path)
            
            if not os.path.exists(source_path):
                return AgentResult(
                    success=False,
                    error="Source not found",
                    message=f"Source does not exist: {source_path}"
                )
            
            # Create destination directory if needed
            if os.path.isdir(source_path):
                shutil.copytree(source_path, dest_path)
                operation = "directory"
            else:
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                shutil.copy2(source_path, dest_path)
                operation = "file"
            
            return AgentResult(
                success=True,
                data={
                    "source": source_path,
                    "destination": dest_path,
                    "operation": f"copy_{operation}"
                },
                message=f"Successfully copied {operation}: {source_path} -> {dest_path}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to copy file/directory"
            )
    
    async def _move_file(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Move or rename file/directory"""
        try:
            source_path = context.get("source") if context else None
            dest_path = context.get("destination") if context else None
            
            if not source_path or not dest_path:
                paths = await self._extract_source_dest_paths(task)
                source_path = source_path or paths.get("source")
                dest_path = dest_path or paths.get("destination")
            
            if not source_path or not dest_path:
                return AgentResult(
                    success=False,
                    error="Missing source or destination path",
                    message="Please specify both source and destination paths"
                )
            
            source_path = self._resolve_path(source_path)
            dest_path = self._resolve_path(dest_path)
            
            if not os.path.exists(source_path):
                return AgentResult(
                    success=False,
                    error="Source not found",
                    message=f"Source does not exist: {source_path}"
                )
            
            # Create destination directory if needed
            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
            
            shutil.move(source_path, dest_path)
            
            return AgentResult(
                success=True,
                data={
                    "source": source_path,
                    "destination": dest_path,
                    "operation": "move"
                },
                message=f"Successfully moved: {source_path} -> {dest_path}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to move file/directory"
            )
    
    async def _delete_file(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Delete file or directory"""
        try:
            file_path = self._extract_file_path(task, context)
            
            if not file_path:
                return AgentResult(
                    success=False,
                    error="No file path specified",
                    message="Please specify a file path to delete"
                )
            
            file_path = self._resolve_path(file_path)
            
            if not os.path.exists(file_path):
                return AgentResult(
                    success=False,
                    error="File not found",
                    message=f"File does not exist: {file_path}"
                )
            
            # Safety check - don't delete important system files
            if self._is_system_file(file_path):
                return AgentResult(
                    success=False,
                    error="Cannot delete system file",
                    message=f"Refusing to delete system file: {file_path}"
                )
            
            if os.path.isdir(file_path):
                shutil.rmtree(file_path)
                operation = "directory"
            else:
                os.remove(file_path)
                operation = "file"
            
            return AgentResult(
                success=True,
                data={
                    "path": file_path,
                    "operation": f"delete_{operation}"
                },
                message=f"Successfully deleted {operation}: {file_path}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to delete file/directory"
            )
    
    async def _create_directory(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Create directory"""
        try:
            dir_path = self._extract_file_path(task, context)
            
            if not dir_path:
                return AgentResult(
                    success=False,
                    error="No directory path specified",
                    message="Please specify a directory path to create"
                )
            
            dir_path = self._resolve_path(dir_path)
            
            os.makedirs(dir_path, exist_ok=True)
            
            return AgentResult(
                success=True,
                data={"path": dir_path},
                message=f"Successfully created directory: {dir_path}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to create directory"
            )
    
    async def _list_directory(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """List directory contents"""
        try:
            dir_path = self._extract_file_path(task, context) or self.working_directory
            dir_path = self._resolve_path(dir_path)
            
            if not os.path.exists(dir_path):
                return AgentResult(
                    success=False,
                    error="Directory not found",
                    message=f"Directory does not exist: {dir_path}"
                )
            
            if not os.path.isdir(dir_path):
                return AgentResult(
                    success=False,
                    error="Not a directory",
                    message=f"Path is not a directory: {dir_path}"
                )
            
            items = []
            for item_name in os.listdir(dir_path):
                item_path = os.path.join(dir_path, item_name)
                item_stat = os.stat(item_path)
                
                items.append({
                    "name": item_name,
                    "path": item_path,
                    "type": "directory" if os.path.isdir(item_path) else "file",
                    "size": item_stat.st_size,
                    "modified": datetime.fromtimestamp(item_stat.st_mtime).isoformat(),
                    "permissions": oct(item_stat.st_mode)[-3:]
                })
            
            # Sort by type (directories first) then by name
            items.sort(key=lambda x: (x["type"] != "directory", x["name"].lower()))
            
            return AgentResult(
                success=True,
                data={
                    "directory": dir_path,
                    "items": items,
                    "count": len(items)
                },
                message=f"Listed {len(items)} items in directory: {dir_path}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to list directory"
            )
    
    async def _search_files(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Search for files"""
        try:
            search_dir = context.get("directory") if context else self.working_directory
            pattern = context.get("pattern") if context else None
            
            if not pattern:
                # Extract pattern from task
                pattern = await self._extract_search_pattern(task)
            
            search_dir = self._resolve_path(search_dir)
            
            if not os.path.exists(search_dir):
                return AgentResult(
                    success=False,
                    error="Search directory not found",
                    message=f"Directory does not exist: {search_dir}"
                )
            
            matches = []
            for root, dirs, files in os.walk(search_dir):
                for file in files:
                    if pattern.lower() in file.lower():
                        file_path = os.path.join(root, file)
                        file_stat = os.stat(file_path)
                        
                        matches.append({
                            "name": file,
                            "path": file_path,
                            "directory": root,
                            "size": file_stat.st_size,
                            "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                        })
            
            return AgentResult(
                success=True,
                data={
                    "pattern": pattern,
                    "search_directory": search_dir,
                    "matches": matches,
                    "count": len(matches)
                },
                message=f"Found {len(matches)} files matching pattern: {pattern}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to search files"
            )
    
    async def _analyze_file(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Analyze file properties and content"""
        try:
            file_path = self._extract_file_path(task, context)
            
            if not file_path:
                return AgentResult(
                    success=False,
                    error="No file path specified",
                    message="Please specify a file path to analyze"
                )
            
            file_path = self._resolve_path(file_path)
            
            if not os.path.exists(file_path):
                return AgentResult(
                    success=False,
                    error="File not found",
                    message=f"File does not exist: {file_path}"
                )
            
            file_stat = os.stat(file_path)
            mime_type, encoding = mimetypes.guess_type(file_path)
            
            analysis = {
                "path": file_path,
                "name": os.path.basename(file_path),
                "extension": os.path.splitext(file_path)[1],
                "size": file_stat.st_size,
                "mime_type": mime_type,
                "encoding": encoding,
                "created": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                "accessed": datetime.fromtimestamp(file_stat.st_atime).isoformat(),
                "permissions": oct(file_stat.st_mode)[-3:],
                "is_directory": os.path.isdir(file_path),
                "is_file": os.path.isfile(file_path),
                "is_link": os.path.islink(file_path)
            }
            
            # Additional analysis for text files
            if mime_type and mime_type.startswith('text') and file_stat.st_size < 1024 * 1024:
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                        content = f.read()
                        analysis.update({
                            "line_count": len(content.splitlines()),
                            "character_count": len(content),
                            "word_count": len(content.split()),
                            "content_preview": content[:500] + "..." if len(content) > 500 else content
                        })
                except Exception:
                    pass
            
            return AgentResult(
                success=True,
                data=analysis,
                message=f"Successfully analyzed file: {file_path}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to analyze file"
            )
    
    async def _general_file_operation(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Handle general file operations"""
        general_prompt = f"""
        You are a file management expert. Help with this file operation:
        
        Task: {task}
        Context: {context or {}}
        Working Directory: {self.working_directory}
        
        Provide specific instructions for the file operation, including:
        1. What operation needs to be performed
        2. Required parameters (file paths, etc.)
        3. Step-by-step instructions
        4. Potential risks or considerations
        """
        
        try:
            response = await llm_manager.generate(
                prompt=general_prompt,
                temperature=0.3
            )
            
            return AgentResult(
                success=True,
                data={"instructions": response},
                message="Generated file operation instructions"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to process general file operation"
            )
    
    def _extract_file_path(self, task: str, context: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """Extract file path from task or context"""
        if context and "path" in context:
            return context["path"]
        
        if context and "file_path" in context:
            return context["file_path"]
        
        # Simple extraction from task text
        import re
        
        # Look for quoted paths
        quoted_matches = re.findall(r'["\']([^"\']+)["\']', task)
        for match in quoted_matches:
            if '/' in match or '\\' in match or '.' in match:
                return match
        
        # Look for file extensions
        ext_matches = re.findall(r'(\S+\.\w+)', task)
        if ext_matches:
            return ext_matches[0]
        
        return None
    
    def _resolve_path(self, path: str) -> str:
        """Resolve relative paths to absolute paths"""
        if os.path.isabs(path):
            return os.path.normpath(path)
        else:
            return os.path.normpath(os.path.join(self.working_directory, path))
    
    def _is_system_file(self, path: str) -> bool:
        """Check if path is a system file that shouldn't be deleted"""
        system_paths = [
            "/bin", "/sbin", "/usr", "/etc", "/var", "/sys", "/proc",
            "C:\\Windows", "C:\\Program Files", "C:\\System32"
        ]
        
        abs_path = os.path.abspath(path)
        return any(abs_path.startswith(sys_path) for sys_path in system_paths)
    
    async def _generate_file_content(self, task: str, file_path: str) -> str:
        """Generate file content based on task description"""
        content_prompt = f"""
        Generate appropriate file content for this task:
        
        Task: {task}
        File Path: {file_path}
        File Extension: {os.path.splitext(file_path)[1]}
        
        Create content that matches the file type and task requirements.
        For code files, include appropriate syntax and structure.
        For text files, provide relevant content.
        For configuration files, use proper format.
        """
        
        try:
            content = await llm_manager.generate(
                prompt=content_prompt,
                temperature=0.4
            )
            return content
        except Exception as e:
            self.logger.error(f"Content generation failed: {e}")
            return f"# Generated content for {file_path}\n# Task: {task}\n"
    
    async def _extract_source_dest_paths(self, task: str) -> Dict[str, Optional[str]]:
        """Extract source and destination paths from task"""
        # Simple pattern matching for common copy/move syntax
        import re
        
        patterns = [
            r'from\s+["\']?([^"\']+)["\']?\s+to\s+["\']?([^"\']+)["\']?',
            r'["\']?([^"\']+)["\']?\s+to\s+["\']?([^"\']+)["\']?',
            r'copy\s+["\']?([^"\']+)["\']?\s+["\']?([^"\']+)["\']?',
            r'move\s+["\']?([^"\']+)["\']?\s+["\']?([^"\']+)["\']?'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, task, re.IGNORECASE)
            if match:
                return {
                    "source": match.group(1),
                    "destination": match.group(2)
                }
        
        return {"source": None, "destination": None}
    
    async def _extract_search_pattern(self, task: str) -> str:
        """Extract search pattern from task"""
        import re
        
        # Look for quoted patterns
        quoted_matches = re.findall(r'["\']([^"\']+)["\']', task)
        if quoted_matches:
            return quoted_matches[0]
        
        # Look for "find" or "search" followed by a word
        find_matches = re.findall(r'(?:find|search|locate)\s+(\S+)', task, re.IGNORECASE)
        if find_matches:
            return find_matches[0]
        
        # Default to searching for any file
        return "*"
