FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies for audio
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    portaudio19-dev \
    python3-pyaudio \
    alsa-utils \
    pulseaudio \
    pulseaudio-utils \
    ffmpeg \
    sox \
    libsox-fmt-all \
    && rm -rf /var/lib/apt/lists/*

# Copy voice-specific requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy voice service code
COPY voice/ ./voice/
COPY core/config.py ./core/
COPY core/__init__.py ./core/

# Create audio directory
RUN mkdir -p /app/audio

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PULSE_RUNTIME_PATH=/var/run/pulse

# Expose port
EXPOSE 8002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8002/health || exit 1

# Run the voice service
CMD ["python", "voice/service.py"]
