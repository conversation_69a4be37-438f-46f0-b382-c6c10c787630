"""
Memory management for Jarvis AI Assistant
Handles vector database operations and conversation memory
"""

import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
import numpy as np

from core.config import config
from core.logger import get_logger

logger = get_logger("memory")


class MemoryItem:
    """Represents a single memory item"""
    
    def __init__(
        self,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        memory_type: str = "conversation",
        importance: float = 0.5,
        timestamp: Optional[datetime] = None
    ):
        self.id = str(uuid.uuid4())
        self.content = content
        self.metadata = metadata or {}
        self.memory_type = memory_type
        self.importance = importance
        self.timestamp = timestamp or datetime.now()
        self.access_count = 0
        self.last_accessed = self.timestamp
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "content": self.content,
            "metadata": self.metadata,
            "memory_type": self.memory_type,
            "importance": self.importance,
            "timestamp": self.timestamp.isoformat(),
            "access_count": self.access_count,
            "last_accessed": self.last_accessed.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MemoryItem":
        """Create from dictionary"""
        item = cls(
            content=data["content"],
            metadata=data.get("metadata", {}),
            memory_type=data.get("memory_type", "conversation"),
            importance=data.get("importance", 0.5),
            timestamp=datetime.fromisoformat(data["timestamp"])
        )
        item.id = data["id"]
        item.access_count = data.get("access_count", 0)
        item.last_accessed = datetime.fromisoformat(
            data.get("last_accessed", data["timestamp"])
        )
        return item


class MemoryManager:
    """Manages conversation memory and knowledge storage"""
    
    def __init__(self):
        self.client = None
        self.collection = None
        self.embedding_model = None
        self.conversation_history: List[MemoryItem] = []
        self.initialize()
    
    def initialize(self):
        """Initialize memory system"""
        try:
            # Initialize ChromaDB
            persist_dir = Path(config.memory.persist_directory)
            persist_dir.mkdir(parents=True, exist_ok=True)
            
            self.client = chromadb.PersistentClient(
                path=str(persist_dir),
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # Get or create collection
            try:
                self.collection = self.client.get_collection(
                    name=config.memory.collection_name
                )
                logger.info("Loaded existing memory collection")
            except:
                self.collection = self.client.create_collection(
                    name=config.memory.collection_name,
                    metadata={"description": "Jarvis conversation memory"}
                )
                logger.info("Created new memory collection")
            
            # Initialize embedding model
            self.embedding_model = SentenceTransformer(
                config.memory.embedding_model
            )
            
            logger.info("Memory manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize memory manager: {e}")
            raise
    
    def create_embedding(self, text: str) -> List[float]:
        """Create embedding for text"""
        try:
            embedding = self.embedding_model.encode(text)
            return embedding.tolist()
        except Exception as e:
            logger.error(f"Failed to create embedding: {e}")
            return []
    
    async def store_memory(
        self,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        memory_type: str = "conversation",
        importance: float = 0.5
    ) -> str:
        """Store a memory item"""
        try:
            memory_item = MemoryItem(
                content=content,
                metadata=metadata,
                memory_type=memory_type,
                importance=importance
            )
            
            # Create embedding
            embedding = self.create_embedding(content)
            
            # Store in vector database
            self.collection.add(
                ids=[memory_item.id],
                embeddings=[embedding],
                documents=[content],
                metadatas=[{
                    **memory_item.metadata,
                    "memory_type": memory_type,
                    "importance": importance,
                    "timestamp": memory_item.timestamp.isoformat(),
                    "access_count": 0
                }]
            )
            
            # Add to conversation history if it's a conversation
            if memory_type == "conversation":
                self.conversation_history.append(memory_item)
                
                # Limit conversation history size
                if len(self.conversation_history) > 100:
                    self.conversation_history = self.conversation_history[-50:]
            
            logger.debug(f"Stored memory: {memory_item.id}")
            return memory_item.id
            
        except Exception as e:
            logger.error(f"Failed to store memory: {e}")
            raise
    
    async def search_memories(
        self,
        query: str,
        memory_type: Optional[str] = None,
        limit: int = 10,
        min_similarity: float = 0.7
    ) -> List[Tuple[MemoryItem, float]]:
        """Search for relevant memories"""
        try:
            # Create query embedding
            query_embedding = self.create_embedding(query)
            
            # Build where clause
            where_clause = {}
            if memory_type:
                where_clause["memory_type"] = memory_type
            
            # Search in vector database
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=limit,
                where=where_clause if where_clause else None
            )
            
            memories = []
            if results["documents"] and results["documents"][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results["documents"][0],
                    results["metadatas"][0],
                    results["distances"][0]
                )):
                    # Convert distance to similarity
                    similarity = 1 - distance
                    
                    if similarity >= min_similarity:
                        memory_item = MemoryItem(
                            content=doc,
                            metadata=metadata,
                            memory_type=metadata.get("memory_type", "conversation"),
                            importance=metadata.get("importance", 0.5),
                            timestamp=datetime.fromisoformat(metadata["timestamp"])
                        )
                        memory_item.id = results["ids"][0][i]
                        memory_item.access_count = metadata.get("access_count", 0)
                        
                        memories.append((memory_item, similarity))
                        
                        # Update access count
                        await self.update_access_count(memory_item.id)
            
            logger.debug(f"Found {len(memories)} relevant memories for query: {query}")
            return memories
            
        except Exception as e:
            logger.error(f"Failed to search memories: {e}")
            return []
    
    async def update_access_count(self, memory_id: str):
        """Update access count for a memory"""
        try:
            # Get current metadata
            result = self.collection.get(ids=[memory_id], include=["metadatas"])
            if result["metadatas"]:
                metadata = result["metadatas"][0]
                metadata["access_count"] = metadata.get("access_count", 0) + 1
                metadata["last_accessed"] = datetime.now().isoformat()
                
                # Update in database
                self.collection.update(
                    ids=[memory_id],
                    metadatas=[metadata]
                )
        except Exception as e:
            logger.error(f"Failed to update access count: {e}")
    
    async def get_conversation_context(
        self,
        limit: int = 10,
        include_system: bool = True
    ) -> List[Dict[str, str]]:
        """Get recent conversation context"""
        try:
            context = []
            
            # Get recent conversation history
            recent_history = self.conversation_history[-limit:]
            
            for memory_item in recent_history:
                role = memory_item.metadata.get("role", "user")
                if not include_system and role == "system":
                    continue
                
                context.append({
                    "role": role,
                    "content": memory_item.content,
                    "timestamp": memory_item.timestamp.isoformat()
                })
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to get conversation context: {e}")
            return []
    
    async def store_conversation_turn(
        self,
        user_message: str,
        assistant_response: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Store a complete conversation turn"""
        base_metadata = metadata or {}
        
        # Store user message
        await self.store_memory(
            content=user_message,
            metadata={**base_metadata, "role": "user"},
            memory_type="conversation"
        )
        
        # Store assistant response
        await self.store_memory(
            content=assistant_response,
            metadata={**base_metadata, "role": "assistant"},
            memory_type="conversation"
        )
    
    async def cleanup_old_memories(self, days: int = 30):
        """Clean up old, low-importance memories"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Get all memories
            all_memories = self.collection.get(include=["metadatas"])
            
            ids_to_delete = []
            for memory_id, metadata in zip(all_memories["ids"], all_memories["metadatas"]):
                timestamp = datetime.fromisoformat(metadata["timestamp"])
                importance = metadata.get("importance", 0.5)
                access_count = metadata.get("access_count", 0)
                
                # Delete if old, low importance, and rarely accessed
                if (timestamp < cutoff_date and 
                    importance < 0.3 and 
                    access_count < 2):
                    ids_to_delete.append(memory_id)
            
            if ids_to_delete:
                self.collection.delete(ids=ids_to_delete)
                logger.info(f"Cleaned up {len(ids_to_delete)} old memories")
            
        except Exception as e:
            logger.error(f"Failed to cleanup memories: {e}")
    
    async def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory statistics"""
        try:
            all_memories = self.collection.get(include=["metadatas"])
            
            total_count = len(all_memories["ids"])
            memory_types = {}
            importance_avg = 0
            
            for metadata in all_memories["metadatas"]:
                memory_type = metadata.get("memory_type", "unknown")
                memory_types[memory_type] = memory_types.get(memory_type, 0) + 1
                importance_avg += metadata.get("importance", 0.5)
            
            if total_count > 0:
                importance_avg /= total_count
            
            return {
                "total_memories": total_count,
                "memory_types": memory_types,
                "average_importance": importance_avg,
                "conversation_history_length": len(self.conversation_history)
            }
            
        except Exception as e:
            logger.error(f"Failed to get memory stats: {e}")
            return {}


# Global memory manager instance
memory_manager = MemoryManager()
