"""
FastAPI Web Server for Jarvis AI Assistant
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import json
import asyncio
from datetime import datetime

from core.logger import get_logger
from jarvis_app import JarvisApp

logger = get_logger("web_server")


class MessageRequest(BaseModel):
    message: str
    context: Optional[Dict[str, Any]] = None


class MessageResponse(BaseModel):
    response: str
    timestamp: str
    session_id: str


class StatusResponse(BaseModel):
    status: str
    system_info: Dict[str, Any]


class WebSocketManager:
    """Manages WebSocket connections"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)
    
    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # Remove dead connections
                self.active_connections.remove(connection)


def create_app(jarvis_app: JarvisApp) -> FastAPI:
    """Create and configure FastAPI application"""
    
    app = FastAPI(
        title="Jarvis AI Assistant",
        description="Web interface for Jarvis AI Assistant",
        version="1.0.0"
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # WebSocket manager
    websocket_manager = WebSocketManager()
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "timestamp": datetime.now().isoformat()}
    
    # Chat endpoint
    @app.post("/chat", response_model=MessageResponse)
    async def chat(request: MessageRequest):
        try:
            if not jarvis_app.initialized:
                raise HTTPException(status_code=503, detail="Jarvis is not initialized")
            
            response = await jarvis_app.process_message(request.message, request.context)
            
            return MessageResponse(
                response=response,
                timestamp=datetime.now().isoformat(),
                session_id=jarvis_app.session_id
            )
            
        except Exception as e:
            logger.error(f"Chat endpoint error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    # System status endpoint
    @app.get("/status", response_model=StatusResponse)
    async def get_status():
        try:
            system_info = await jarvis_app.get_system_status()
            
            return StatusResponse(
                status="active" if jarvis_app.initialized else "inactive",
                system_info=system_info
            )
            
        except Exception as e:
            logger.error(f"Status endpoint error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    # Conversation history endpoint
    @app.get("/conversation")
    async def get_conversation_history(limit: int = 10):
        try:
            history = await jarvis_app.get_conversation_history(limit)
            return {"conversation": history}
            
        except Exception as e:
            logger.error(f"Conversation endpoint error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    # Clear conversation endpoint
    @app.delete("/conversation")
    async def clear_conversation():
        try:
            await jarvis_app.clear_conversation_history()
            return {"message": "Conversation history cleared"}
            
        except Exception as e:
            logger.error(f"Clear conversation endpoint error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    # Agents endpoint
    @app.get("/agents")
    async def get_agents():
        try:
            from agents.agent_dispatcher import dispatcher
            agents = dispatcher.list_agents()
            return {"agents": agents}
            
        except Exception as e:
            logger.error(f"Agents endpoint error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    # Skills endpoint
    @app.get("/skills")
    async def get_skills():
        try:
            from skills.skill_manager import skill_manager
            skills = skill_manager.list_skills()
            return {"skills": skills}
            
        except Exception as e:
            logger.error(f"Skills endpoint error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    # Memory stats endpoint
    @app.get("/memory")
    async def get_memory_stats():
        try:
            from core.memory import memory_manager
            stats = await memory_manager.get_memory_stats()
            return {"memory": stats}
            
        except Exception as e:
            logger.error(f"Memory endpoint error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    # WebSocket endpoint for real-time chat
    @app.websocket("/ws")
    async def websocket_endpoint(websocket: WebSocket):
        await websocket_manager.connect(websocket)
        try:
            while True:
                # Receive message from client
                data = await websocket.receive_text()
                
                try:
                    message_data = json.loads(data)
                    message = message_data.get("message", "")
                    context = message_data.get("context", {})
                    
                    if message:
                        # Process message with Jarvis
                        response = await jarvis_app.process_message(message, context)
                        
                        # Send response back
                        response_data = {
                            "type": "response",
                            "message": response,
                            "timestamp": datetime.now().isoformat(),
                            "session_id": jarvis_app.session_id
                        }
                        
                        await websocket_manager.send_personal_message(
                            json.dumps(response_data), 
                            websocket
                        )
                    
                except json.JSONDecodeError:
                    # Handle plain text messages
                    response = await jarvis_app.process_message(data)
                    
                    response_data = {
                        "type": "response",
                        "message": response,
                        "timestamp": datetime.now().isoformat(),
                        "session_id": jarvis_app.session_id
                    }
                    
                    await websocket_manager.send_personal_message(
                        json.dumps(response_data), 
                        websocket
                    )
                
        except WebSocketDisconnect:
            websocket_manager.disconnect(websocket)
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
            websocket_manager.disconnect(websocket)
    
    # Simple web interface
    @app.get("/", response_class=HTMLResponse)
    async def get_web_interface():
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Jarvis AI Assistant</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 10px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    overflow: hidden;
                }
                .header {
                    background: #2c3e50;
                    color: white;
                    padding: 20px;
                    text-align: center;
                }
                .chat-container {
                    height: 500px;
                    overflow-y: auto;
                    padding: 20px;
                    background: #f8f9fa;
                }
                .message {
                    margin: 10px 0;
                    padding: 10px 15px;
                    border-radius: 20px;
                    max-width: 80%;
                }
                .user-message {
                    background: #007bff;
                    color: white;
                    margin-left: auto;
                    text-align: right;
                }
                .bot-message {
                    background: #e9ecef;
                    color: #333;
                }
                .input-container {
                    padding: 20px;
                    background: white;
                    border-top: 1px solid #dee2e6;
                }
                .input-group {
                    display: flex;
                    gap: 10px;
                }
                input[type="text"] {
                    flex: 1;
                    padding: 12px;
                    border: 2px solid #dee2e6;
                    border-radius: 25px;
                    outline: none;
                    font-size: 16px;
                }
                input[type="text"]:focus {
                    border-color: #007bff;
                }
                button {
                    padding: 12px 24px;
                    background: #007bff;
                    color: white;
                    border: none;
                    border-radius: 25px;
                    cursor: pointer;
                    font-size: 16px;
                }
                button:hover {
                    background: #0056b3;
                }
                .status {
                    padding: 10px;
                    text-align: center;
                    font-size: 14px;
                    color: #666;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🤖 Jarvis AI Assistant</h1>
                    <p>Your intelligent AI companion</p>
                </div>
                
                <div class="chat-container" id="chatContainer">
                    <div class="message bot-message">
                        Hello! I'm Jarvis, your AI assistant. How can I help you today?
                    </div>
                </div>
                
                <div class="input-container">
                    <div class="input-group">
                        <input type="text" id="messageInput" placeholder="Type your message here..." 
                               onkeypress="handleKeyPress(event)">
                        <button onclick="sendMessage()">Send</button>
                    </div>
                </div>
                
                <div class="status" id="status">
                    Ready
                </div>
            </div>

            <script>
                let ws = null;
                
                function connectWebSocket() {
                    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    ws = new WebSocket(`${protocol}//${window.location.host}/ws`);
                    
                    ws.onopen = function(event) {
                        updateStatus('Connected');
                    };
                    
                    ws.onmessage = function(event) {
                        const data = JSON.parse(event.data);
                        addMessage(data.message, 'bot');
                        updateStatus('Ready');
                    };
                    
                    ws.onclose = function(event) {
                        updateStatus('Disconnected');
                        setTimeout(connectWebSocket, 3000);
                    };
                    
                    ws.onerror = function(error) {
                        updateStatus('Connection error');
                    };
                }
                
                function sendMessage() {
                    const input = document.getElementById('messageInput');
                    const message = input.value.trim();
                    
                    if (message && ws && ws.readyState === WebSocket.OPEN) {
                        addMessage(message, 'user');
                        ws.send(JSON.stringify({message: message}));
                        input.value = '';
                        updateStatus('Thinking...');
                    }
                }
                
                function addMessage(text, sender) {
                    const chatContainer = document.getElementById('chatContainer');
                    const messageDiv = document.createElement('div');
                    messageDiv.className = `message ${sender}-message`;
                    messageDiv.textContent = text;
                    chatContainer.appendChild(messageDiv);
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }
                
                function updateStatus(text) {
                    document.getElementById('status').textContent = text;
                }
                
                function handleKeyPress(event) {
                    if (event.key === 'Enter') {
                        sendMessage();
                    }
                }
                
                // Connect on page load
                connectWebSocket();
            </script>
        </body>
        </html>
        """
        return HTMLResponse(content=html_content)
    
    return app
