"""
Base Skill class for Jarvis AI Assistant
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from core.logger import get_logger


@dataclass
class SkillResult:
    """Result of skill execution"""
    success: bool
    data: Any = None
    message: str = ""
    error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "success": self.success,
            "data": self.data,
            "message": self.message,
            "error": self.error
        }


class BaseSkill(ABC):
    """Base class for all skills"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.version = "1.0.0"
        self.author = "Jarvis AI"
        self.keywords: List[str] = []
        self.dependencies: List[str] = []
        self.enabled = True
        self.logger = get_logger(f"skills.{name}")
    
    @abstractmethod
    async def execute(
        self,
        command: str,
        parameters: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> SkillResult:
        """Execute the skill"""
        pass
    
    @abstractmethod
    def can_handle(self, command: str, parameters: Optional[Dict[str, Any]] = None) -> bool:
        """Check if this skill can handle the given command"""
        pass
    
    def get_info(self) -> Dict[str, Any]:
        """Get skill information"""
        return {
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "author": self.author,
            "keywords": self.keywords,
            "dependencies": self.dependencies,
            "enabled": self.enabled
        }
    
    def get_help(self) -> str:
        """Get help text for this skill"""
        return f"""
Skill: {self.name}
Description: {self.description}
Version: {self.version}
Keywords: {', '.join(self.keywords)}

Usage: Describe how to use this skill
        """.strip()
    
    async def initialize(self) -> bool:
        """Initialize the skill (called when skill is loaded)"""
        try:
            self.logger.info(f"Initializing skill: {self.name}")
            return True
        except Exception as e:
            self.logger.error(f"Skill initialization failed: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup resources (called when skill is unloaded)"""
        try:
            self.logger.info(f"Cleaning up skill: {self.name}")
        except Exception as e:
            self.logger.error(f"Skill cleanup failed: {e}")
    
    def enable(self):
        """Enable the skill"""
        self.enabled = True
        self.logger.info(f"Skill enabled: {self.name}")
    
    def disable(self):
        """Disable the skill"""
        self.enabled = False
        self.logger.info(f"Skill disabled: {self.name}")
    
    def check_dependencies(self) -> List[str]:
        """Check if all dependencies are available"""
        missing_deps = []
        
        for dep in self.dependencies:
            try:
                __import__(dep)
            except ImportError:
                missing_deps.append(dep)
        
        return missing_deps
