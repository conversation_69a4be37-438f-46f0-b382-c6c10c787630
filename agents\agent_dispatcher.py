"""
Agent Dispatcher for Jarvis AI Assistant
Manages and coordinates multiple agents
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Type
from datetime import datetime
from .base_agent import BaseAgent, AgentResult, AgentStatus
from .planner_agent import PlannerAgent
from .developer_agent import DeveloperAgent
from .shell_agent import ShellAgent
from .file_manager_agent import FileManagerAgent
from .browser_agent import BrowserAgent
from .system_control_agent import SystemControlAgent
from .research_agent import ResearchAgent
from core.llm import llm_manager
from core.memory import memory_manager
from core.logger import get_logger

logger = get_logger("dispatcher")


class AgentDispatcher:
    """Manages and coordinates multiple agents"""
    
    def __init__(self):
        self.agents: Dict[str, BaseAgent] = {}
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        self.task_queue: List[Dict[str, Any]] = []
        self.max_concurrent_tasks = 5
        self.initialize_agents()
    
    def initialize_agents(self):
        """Initialize all available agents"""
        agent_classes = [
            PlannerAgent,
            DeveloperAgent,
            ShellAgent,
            FileManagerAgent,
            BrowserAgent,
            SystemControlAgent,
            ResearchAgent
        ]
        
        for agent_class in agent_classes:
            try:
                agent = agent_class()
                self.agents[agent.name] = agent
                logger.info(f"Initialized agent: {agent.name}")
            except Exception as e:
                logger.error(f"Failed to initialize agent {agent_class.__name__}: {e}")
        
        logger.info(f"Initialized {len(self.agents)} agents")
    
    async def dispatch_task(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None,
        preferred_agent: Optional[str] = None,
        **kwargs
    ) -> AgentResult:
        """Dispatch a task to the most appropriate agent"""
        try:
            logger.info(f"Dispatching task: {task}")
            
            # Generate unique task ID
            task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            # Select agent
            if preferred_agent and preferred_agent in self.agents:
                selected_agent = self.agents[preferred_agent]
                logger.info(f"Using preferred agent: {preferred_agent}")
            else:
                selected_agent = await self._select_agent(task, context)
                if not selected_agent:
                    return AgentResult(
                        success=False,
                        error="No suitable agent found",
                        message="Could not find an agent capable of handling this task"
                    )
            
            # Check if agent can handle the task
            if not selected_agent.can_handle(task, context):
                logger.warning(f"Agent {selected_agent.name} cannot handle task, trying alternative")
                selected_agent = await self._find_alternative_agent(task, context, selected_agent.name)
                
                if not selected_agent:
                    return AgentResult(
                        success=False,
                        error="No capable agent found",
                        message="No agent can handle this task"
                    )
            
            # Execute task
            logger.info(f"Executing task with agent: {selected_agent.name}")
            
            # Store task info
            self.active_tasks[task_id] = {
                "task": task,
                "context": context,
                "agent": selected_agent.name,
                "start_time": datetime.now(),
                "status": "executing"
            }
            
            try:
                # Execute with thinking and reflection
                result = await selected_agent.execute_with_thinking(task, context, **kwargs)
                
                # Update task status
                self.active_tasks[task_id]["status"] = "completed" if result.success else "failed"
                self.active_tasks[task_id]["end_time"] = datetime.now()
                self.active_tasks[task_id]["result"] = result.to_dict()
                
                # Store execution in memory
                await memory_manager.store_memory(
                    content=f"Task: {task}\nAgent: {selected_agent.name}\nResult: {result.message}",
                    metadata={
                        "task_id": task_id,
                        "agent": selected_agent.name,
                        "success": result.success,
                        "task_type": "execution"
                    },
                    memory_type="task",
                    importance=0.8 if result.success else 0.9
                )
                
                logger.info(f"Task completed: {task_id} - {result.message}")
                return result
                
            except Exception as e:
                logger.error(f"Task execution failed: {e}")
                self.active_tasks[task_id]["status"] = "error"
                self.active_tasks[task_id]["error"] = str(e)
                
                return AgentResult(
                    success=False,
                    error=str(e),
                    message=f"Task execution failed with agent {selected_agent.name}"
                )
            
            finally:
                # Clean up completed task
                if task_id in self.active_tasks:
                    # Move to history or remove after some time
                    pass
        
        except Exception as e:
            logger.error(f"Task dispatch failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="Task dispatch failed"
            )
    
    async def _select_agent(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Optional[BaseAgent]:
        """Select the most appropriate agent for a task"""
        try:
            # Get agent recommendations from LLM
            agent_selection_prompt = f"""
            Select the most appropriate agent for this task:
            
            Task: {task}
            Context: {context or {}}
            
            Available agents:
            {self._get_agent_descriptions()}
            
            Consider:
            1. Agent capabilities
            2. Task requirements
            3. Context information
            
            Respond with just the agent name (one of: {', '.join(self.agents.keys())})
            """
            
            response = await llm_manager.generate(
                prompt=agent_selection_prompt,
                temperature=0.1
            )
            
            suggested_agent = response.strip().lower()
            
            # Validate suggestion
            if suggested_agent in self.agents:
                return self.agents[suggested_agent]
            
            # Fallback: check each agent's can_handle method
            for agent in self.agents.values():
                if agent.can_handle(task, context):
                    logger.info(f"Fallback selection: {agent.name}")
                    return agent
            
            return None
            
        except Exception as e:
            logger.error(f"Agent selection failed: {e}")
            
            # Emergency fallback: try each agent
            for agent in self.agents.values():
                if agent.can_handle(task, context):
                    return agent
            
            return None
    
    async def _find_alternative_agent(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None,
        exclude_agent: str = None
    ) -> Optional[BaseAgent]:
        """Find an alternative agent when the first choice fails"""
        for agent_name, agent in self.agents.items():
            if agent_name != exclude_agent and agent.can_handle(task, context):
                logger.info(f"Found alternative agent: {agent_name}")
                return agent
        
        return None
    
    def _get_agent_descriptions(self) -> str:
        """Get descriptions of all available agents"""
        descriptions = []
        for agent_name, agent in self.agents.items():
            descriptions.append(f"- {agent_name}: {agent.description}")
        
        return "\n".join(descriptions)
    
    async def execute_plan(
        self,
        plan: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Execute a multi-step plan"""
        try:
            logger.info("Executing multi-step plan")
            
            # Get execution order
            planner = self.agents.get("planner")
            if not planner:
                return AgentResult(
                    success=False,
                    error="Planner agent not available",
                    message="Cannot execute plan without planner agent"
                )
            
            execution_order = await planner.create_execution_order(plan)
            
            if not execution_order:
                return AgentResult(
                    success=False,
                    error="Failed to create execution order",
                    message="Could not determine step execution order"
                )
            
            # Execute steps
            results = []
            completed_steps = set()
            
            for step in execution_order:
                step_id = step.get("id")
                description = step.get("description", "")
                agent_name = step.get("agent", "general")
                dependencies = step.get("dependencies", [])
                
                # Check dependencies
                if not all(dep in completed_steps for dep in dependencies):
                    logger.warning(f"Step {step_id} dependencies not met, skipping")
                    continue
                
                logger.info(f"Executing step {step_id}: {description}")
                
                # Execute step
                step_context = {
                    **(context or {}),
                    "step_id": step_id,
                    "plan": plan,
                    "completed_steps": list(completed_steps)
                }
                
                result = await self.dispatch_task(
                    task=description,
                    context=step_context,
                    preferred_agent=agent_name
                )
                
                results.append({
                    "step_id": step_id,
                    "description": description,
                    "agent": agent_name,
                    "result": result.to_dict()
                })
                
                if result.success:
                    completed_steps.add(step_id)
                    logger.info(f"Step {step_id} completed successfully")
                else:
                    logger.error(f"Step {step_id} failed: {result.error}")
                    
                    # Decide whether to continue or stop
                    if step.get("critical", True):
                        logger.error("Critical step failed, stopping execution")
                        break
            
            # Calculate overall success
            total_steps = len(execution_order)
            successful_steps = len(completed_steps)
            success_rate = successful_steps / total_steps if total_steps > 0 else 0
            
            overall_success = success_rate >= 0.8  # 80% success rate threshold
            
            return AgentResult(
                success=overall_success,
                data={
                    "plan": plan,
                    "execution_order": execution_order,
                    "results": results,
                    "completed_steps": list(completed_steps),
                    "success_rate": success_rate,
                    "total_steps": total_steps,
                    "successful_steps": successful_steps
                },
                message=f"Plan execution completed: {successful_steps}/{total_steps} steps successful"
            )
            
        except Exception as e:
            logger.error(f"Plan execution failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="Plan execution failed"
            )
    
    async def get_agent_status(self, agent_name: Optional[str] = None) -> Dict[str, Any]:
        """Get status of agents"""
        if agent_name:
            if agent_name in self.agents:
                return self.agents[agent_name].get_status()
            else:
                return {"error": f"Agent {agent_name} not found"}
        else:
            return {
                agent_name: agent.get_status()
                for agent_name, agent in self.agents.items()
            }
    
    async def get_active_tasks(self) -> Dict[str, Any]:
        """Get information about active tasks"""
        return {
            "active_tasks": self.active_tasks,
            "task_count": len(self.active_tasks),
            "queue_size": len(self.task_queue)
        }
    
    async def cancel_task(self, task_id: str) -> bool:
        """Cancel an active task"""
        if task_id in self.active_tasks:
            self.active_tasks[task_id]["status"] = "cancelled"
            logger.info(f"Cancelled task: {task_id}")
            return True
        return False
    
    def list_agents(self) -> List[Dict[str, Any]]:
        """List all available agents"""
        return [
            {
                "name": agent.name,
                "description": agent.description,
                "capabilities": agent.capabilities,
                "status": agent.status.value
            }
            for agent in self.agents.values()
        ]
    
    async def get_agent_recommendations(self, task: str) -> List[Dict[str, Any]]:
        """Get agent recommendations for a task"""
        recommendations = []
        
        for agent_name, agent in self.agents.items():
            can_handle = agent.can_handle(task)
            
            # Get confidence score from LLM
            confidence_prompt = f"""
            Rate how well this agent can handle the task (0-10):
            
            Agent: {agent.name} - {agent.description}
            Capabilities: {', '.join(agent.capabilities)}
            Task: {task}
            
            Respond with just a number from 0-10.
            """
            
            try:
                confidence_response = await llm_manager.generate(
                    prompt=confidence_prompt,
                    temperature=0.1
                )
                confidence = float(confidence_response.strip())
            except:
                confidence = 5.0 if can_handle else 1.0
            
            recommendations.append({
                "agent": agent_name,
                "description": agent.description,
                "can_handle": can_handle,
                "confidence": confidence,
                "capabilities": agent.capabilities
            })
        
        # Sort by confidence
        recommendations.sort(key=lambda x: x["confidence"], reverse=True)
        
        return recommendations


# Global dispatcher instance
dispatcher = AgentDispatcher()
