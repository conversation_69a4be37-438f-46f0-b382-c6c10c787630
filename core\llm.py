"""
LLM Manager for Jarvis AI Assistant
Handles communication with various LLM providers
"""

import asyncio
from typing import Dict, List, Optional, Any, AsyncGenerator
from abc import ABC, abstractmethod
import json
from groq import Groq
import openai
from core.config import config
from core.logger import get_logger

logger = get_logger("llm")


class BaseLLMProvider(ABC):
    """Base class for LLM providers"""
    
    @abstractmethod
    async def generate(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 4096,
        **kwargs
    ) -> str:
        """Generate text completion"""
        pass
    
    @abstractmethod
    async def stream_generate(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 4096,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate streaming text completion"""
        pass


class GroqProvider(BaseLLMProvider):
    """Groq LLM provider"""
    
    def __init__(self, api_key: str, model: str = "mixtral-8x7b-32768"):
        self.client = Groq(api_key=api_key)
        self.model = model
        logger.info(f"Initialized Groq provider with model: {model}")
    
    async def generate(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 4096,
        **kwargs
    ) -> str:
        """Generate text completion using Groq"""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Groq generation error: {e}")
            raise
    
    async def stream_generate(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 4096,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate streaming text completion using Groq"""
        try:
            stream = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True,
                **kwargs
            )
            
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
        except Exception as e:
            logger.error(f"Groq streaming error: {e}")
            raise


class OpenAIProvider(BaseLLMProvider):
    """OpenAI LLM provider"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo"):
        self.client = openai.AsyncOpenAI(api_key=api_key)
        self.model = model
        logger.info(f"Initialized OpenAI provider with model: {model}")
    
    async def generate(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 4096,
        **kwargs
    ) -> str:
        """Generate text completion using OpenAI"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI generation error: {e}")
            raise
    
    async def stream_generate(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 4096,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate streaming text completion using OpenAI"""
        try:
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True,
                **kwargs
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
        except Exception as e:
            logger.error(f"OpenAI streaming error: {e}")
            raise


class LLMManager:
    """Main LLM manager that handles multiple providers"""
    
    def __init__(self):
        self.providers: Dict[str, BaseLLMProvider] = {}
        self.current_provider = None
        self.initialize_providers()
    
    def initialize_providers(self):
        """Initialize available LLM providers"""
        # Initialize Groq
        groq_key = config.get_api_key('groq')
        if groq_key:
            self.providers['groq'] = GroqProvider(
                api_key=groq_key,
                model=config.llm.model
            )
            logger.info("Groq provider initialized")
        
        # Initialize OpenAI
        openai_key = config.get_api_key('openai')
        if openai_key:
            self.providers['openai'] = OpenAIProvider(
                api_key=openai_key,
                model="gpt-3.5-turbo"
            )
            logger.info("OpenAI provider initialized")
        
        # Set current provider
        if config.llm.provider in self.providers:
            self.current_provider = config.llm.provider
            logger.info(f"Set current provider to: {self.current_provider}")
        elif self.providers:
            self.current_provider = list(self.providers.keys())[0]
            logger.warning(f"Fallback to provider: {self.current_provider}")
        else:
            raise ValueError("No LLM providers available")
    
    def get_provider(self, provider_name: Optional[str] = None) -> BaseLLMProvider:
        """Get LLM provider instance"""
        provider_name = provider_name or self.current_provider
        if provider_name not in self.providers:
            raise ValueError(f"Provider {provider_name} not available")
        return self.providers[provider_name]
    
    async def generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        provider: Optional[str] = None,
        temperature: float = None,
        max_tokens: int = None,
        **kwargs
    ) -> str:
        """Generate text completion"""
        messages = []
        
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        messages.append({"role": "user", "content": prompt})
        
        provider_instance = self.get_provider(provider)
        
        return await provider_instance.generate(
            messages=messages,
            temperature=temperature or config.llm.temperature,
            max_tokens=max_tokens or config.llm.max_tokens,
            **kwargs
        )
    
    async def chat(
        self,
        messages: List[Dict[str, str]],
        provider: Optional[str] = None,
        temperature: float = None,
        max_tokens: int = None,
        **kwargs
    ) -> str:
        """Generate chat completion"""
        provider_instance = self.get_provider(provider)
        
        return await provider_instance.generate(
            messages=messages,
            temperature=temperature or config.llm.temperature,
            max_tokens=max_tokens or config.llm.max_tokens,
            **kwargs
        )
    
    async def stream_generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        provider: Optional[str] = None,
        temperature: float = None,
        max_tokens: int = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate streaming text completion"""
        messages = []
        
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        messages.append({"role": "user", "content": prompt})
        
        provider_instance = self.get_provider(provider)
        
        async for chunk in provider_instance.stream_generate(
            messages=messages,
            temperature=temperature or config.llm.temperature,
            max_tokens=max_tokens or config.llm.max_tokens,
            **kwargs
        ):
            yield chunk
    
    def list_providers(self) -> List[str]:
        """List available providers"""
        return list(self.providers.keys())
    
    def switch_provider(self, provider_name: str):
        """Switch to a different provider"""
        if provider_name not in self.providers:
            raise ValueError(f"Provider {provider_name} not available")
        
        self.current_provider = provider_name
        logger.info(f"Switched to provider: {provider_name}")


# Global LLM manager instance
llm_manager = LLMManager()
