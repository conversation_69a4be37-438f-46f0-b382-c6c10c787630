"""
Jarvis AI Assistant - Agent System
Multi-agent framework for task execution
"""

from .base_agent import BaseAgent, AgentR<PERSON>ult
from .planner_agent import PlannerAgent
from .developer_agent import DeveloperAgent
from .shell_agent import ShellAgent
from .file_manager_agent import FileManagerAgent
from .browser_agent import BrowserAgent
from .system_control_agent import SystemControlAgent
from .research_agent import ResearchAgent
from .agent_dispatcher import Agent<PERSON><PERSON>patcher

__all__ = [
    "BaseAgent",
    "AgentResult", 
    "PlannerAgent",
    "DeveloperAgent",
    "ShellAgent",
    "FileManagerAgent",
    "BrowserAgent",
    "SystemControlAgent",
    "ResearchAgent",
    "AgentDispatcher"
]
