"""
Setup script for Jarvis AI Assistant
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_path.exists():
    requirements = requirements_path.read_text().strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]

setup(
    name="jarvis-ai-assistant",
    version="1.0.0",
    author="Jarvis AI Team",
    author_email="<EMAIL>",
    description="A powerful, multi-agent AI assistant with voice control and system integration",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/jarvis-ai/jarvis-assistant",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: System :: Systems Administration",
        "Topic :: Multimedia :: Sound/Audio :: Speech",
    ],
    python_requires=">=3.11",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "isort>=5.12.0",
            "mypy>=1.0.0",
            "flake8>=6.0.0",
            "pre-commit>=3.0.0",
        ],
        "voice": [
            "pyaudio>=0.2.11",
            "speech-recognition>=3.10.0",
            "pyttsx3>=2.90",
            "whisper>=1.1.10",
            "pvporcupine>=3.0.0",
        ],
        "web": [
            "fastapi>=0.104.1",
            "uvicorn>=0.24.0",
            "websockets>=11.0.0",
        ],
        "browser": [
            "playwright>=1.40.0",
            "selenium>=4.15.2",
            "beautifulsoup4>=4.12.2",
        ],
        "all": [
            "pyaudio>=0.2.11",
            "speech-recognition>=3.10.0",
            "pyttsx3>=2.90",
            "whisper>=1.1.10",
            "pvporcupine>=3.0.0",
            "fastapi>=0.104.1",
            "uvicorn>=0.24.0",
            "websockets>=11.0.0",
            "playwright>=1.40.0",
            "selenium>=4.15.2",
            "beautifulsoup4>=4.12.2",
        ]
    },
    entry_points={
        "console_scripts": [
            "jarvis=cli:cli",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json", "*.txt", "*.md"],
    },
    project_urls={
        "Bug Reports": "https://github.com/jarvis-ai/jarvis-assistant/issues",
        "Source": "https://github.com/jarvis-ai/jarvis-assistant",
        "Documentation": "https://jarvis-ai.readthedocs.io/",
    },
    keywords=[
        "ai", "assistant", "voice-control", "automation", "chatbot",
        "llm", "agents", "system-control", "productivity"
    ],
)
