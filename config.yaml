# Jarvis Configuration
app:
  name: "<PERSON> AI Assistant"
  version: "1.0.0"
  debug: true
  host: "0.0.0.0"
  port: 8000

# LLM Configuration
llm:
  provider: "groq"
  model: "mixtral-8x7b-32768"
  temperature: 0.7
  max_tokens: 4096
  timeout: 30

# API Keys (set via environment variables)
api_keys:
  groq_api_key: "${GROQ_API_KEY}"
  openai_api_key: "${OPENAI_API_KEY}"
  elevenlabs_api_key: "${ELEVENLABS_API_KEY}"

# Voice Configuration
voice:
  enabled: true
  wake_word: "jarvis"
  stt_provider: "whisper"  # whisper, google, azure
  tts_provider: "pyttsx3"  # pyttsx3, elevenlabs, edge
  language: "en-US"
  voice_rate: 200
  voice_volume: 0.9

# Memory Configuration
memory:
  provider: "chromadb"
  persist_directory: "./data/memory"
  collection_name: "jarvis_memory"
  embedding_model: "all-MiniLM-L6-v2"
  max_memory_items: 10000

# Agent Configuration
agents:
  max_concurrent: 5
  timeout: 300
  retry_attempts: 3
  
  # Available agents
  available:
    - planner
    - developer
    - shell
    - file_manager
    - browser
    - system_control
    - research

# Skills Configuration
skills:
  auto_load: true
  skills_directory: "./skills"
  max_execution_time: 600

# System Control
system:
  allow_system_commands: true
  safe_mode: false
  restricted_commands:
    - "rm -rf"
    - "del /f /s /q"
    - "format"
    - "shutdown"

# Docker Configuration
docker:
  enabled: true
  network: "jarvis_network"
  volumes:
    - "./data:/app/data"
    - "./skills:/app/skills"

# Logging
logging:
  level: "INFO"
  file: "./logs/jarvis.log"
  max_size: "10MB"
  backup_count: 5
