"""
Text-to-Speech Manager for Jarvis AI Assistant
"""

import asyncio
import io
import threading
from typing import Optional, List
import pyttsx3
import requests
from pathlib import Path
from core.config import config
from core.logger import get_logger

logger = get_logger("tts")


class TTSManager:
    """Manages text-to-speech functionality"""
    
    def __init__(self):
        self.provider = config.voice.tts_provider
        self.voice_rate = config.voice.voice_rate
        self.voice_volume = config.voice.voice_volume
        self.engine = None
        self.is_speaking = False
        self.speech_queue = asyncio.Queue()
        
        self.initialize()
    
    def initialize(self):
        """Initialize TTS system"""
        try:
            if self.provider == "pyttsx3":
                self._initialize_pyttsx3()
            elif self.provider == "elevenlabs":
                self._initialize_elevenlabs()
            elif self.provider == "edge":
                self._initialize_edge_tts()
            
            logger.info(f"TTS initialized with provider: {self.provider}")
            
        except Exception as e:
            logger.error(f"TTS initialization failed: {e}")
            raise
    
    def _initialize_pyttsx3(self):
        """Initialize pyttsx3 TTS engine"""
        try:
            self.engine = pyttsx3.init()
            
            # Set voice properties
            self.engine.setProperty('rate', self.voice_rate)
            self.engine.setProperty('volume', self.voice_volume)
            
            # Try to set a better voice if available
            voices = self.engine.getProperty('voices')
            if voices:
                # Prefer female voices or specific voice names
                preferred_voices = ['zira', 'hazel', 'female']
                selected_voice = None
                
                for voice in voices:
                    voice_name = voice.name.lower()
                    if any(pref in voice_name for pref in preferred_voices):
                        selected_voice = voice.id
                        break
                
                if selected_voice:
                    self.engine.setProperty('voice', selected_voice)
                    logger.info(f"Selected voice: {selected_voice}")
                else:
                    # Use first available voice
                    self.engine.setProperty('voice', voices[0].id)
            
            logger.info("pyttsx3 TTS engine initialized")
            
        except Exception as e:
            logger.error(f"pyttsx3 initialization failed: {e}")
            raise
    
    def _initialize_elevenlabs(self):
        """Initialize ElevenLabs TTS"""
        api_key = config.get_api_key('elevenlabs')
        if not api_key:
            raise ValueError("ElevenLabs API key not found")
        
        self.elevenlabs_api_key = api_key
        self.elevenlabs_voice_id = "21m00Tcm4TlvDq8ikWAM"  # Default voice
        logger.info("ElevenLabs TTS initialized")
    
    def _initialize_edge_tts(self):
        """Initialize Edge TTS"""
        try:
            import edge_tts
            self.edge_tts = edge_tts
            self.edge_voice = "en-US-AriaNeural"  # Default voice
            logger.info("Edge TTS initialized")
        except ImportError:
            logger.error("edge-tts package not installed")
            raise
    
    async def speak(self, text: str, interrupt: bool = False) -> bool:
        """Speak the given text"""
        try:
            if not text or not text.strip():
                return False
            
            text = text.strip()
            logger.info(f"Speaking: {text[:50]}{'...' if len(text) > 50 else ''}")
            
            if interrupt and self.is_speaking:
                await self.stop_speaking()
            
            self.is_speaking = True
            
            try:
                if self.provider == "pyttsx3":
                    success = await self._speak_pyttsx3(text)
                elif self.provider == "elevenlabs":
                    success = await self._speak_elevenlabs(text)
                elif self.provider == "edge":
                    success = await self._speak_edge_tts(text)
                else:
                    logger.error(f"Unknown TTS provider: {self.provider}")
                    success = False
                
                return success
                
            finally:
                self.is_speaking = False
                
        except Exception as e:
            logger.error(f"Speech failed: {e}")
            self.is_speaking = False
            return False
    
    async def _speak_pyttsx3(self, text: str) -> bool:
        """Speak using pyttsx3"""
        try:
            # Run in thread to avoid blocking
            def speak_thread():
                self.engine.say(text)
                self.engine.runAndWait()
            
            thread = threading.Thread(target=speak_thread)
            thread.daemon = True
            thread.start()
            
            # Wait for completion
            while thread.is_alive():
                await asyncio.sleep(0.1)
            
            return True
            
        except Exception as e:
            logger.error(f"pyttsx3 speech failed: {e}")
            return False
    
    async def _speak_elevenlabs(self, text: str) -> bool:
        """Speak using ElevenLabs API"""
        try:
            url = f"https://api.elevenlabs.io/v1/text-to-speech/{self.elevenlabs_voice_id}"
            
            headers = {
                "Accept": "audio/mpeg",
                "Content-Type": "application/json",
                "xi-api-key": self.elevenlabs_api_key
            }
            
            data = {
                "text": text,
                "model_id": "eleven_monolingual_v1",
                "voice_settings": {
                    "stability": 0.5,
                    "similarity_boost": 0.5
                }
            }
            
            # Make API request
            response = requests.post(url, json=data, headers=headers, timeout=30)
            response.raise_for_status()
            
            # Save and play audio
            audio_file = "temp_speech.mp3"
            with open(audio_file, "wb") as f:
                f.write(response.content)
            
            # Play audio file
            await self._play_audio_file(audio_file)
            
            # Clean up
            Path(audio_file).unlink(missing_ok=True)
            
            return True
            
        except Exception as e:
            logger.error(f"ElevenLabs speech failed: {e}")
            return False
    
    async def _speak_edge_tts(self, text: str) -> bool:
        """Speak using Edge TTS"""
        try:
            # Generate speech
            communicate = self.edge_tts.Communicate(text, self.edge_voice)
            
            # Save to file
            audio_file = "temp_speech.wav"
            await communicate.save(audio_file)
            
            # Play audio file
            await self._play_audio_file(audio_file)
            
            # Clean up
            Path(audio_file).unlink(missing_ok=True)
            
            return True
            
        except Exception as e:
            logger.error(f"Edge TTS speech failed: {e}")
            return False
    
    async def _play_audio_file(self, file_path: str):
        """Play audio file"""
        try:
            import platform
            import subprocess
            
            system = platform.system()
            
            if system == "Windows":
                # Use Windows Media Player
                subprocess.run(["start", file_path], shell=True, check=True)
            elif system == "Darwin":  # macOS
                subprocess.run(["afplay", file_path], check=True)
            else:  # Linux
                # Try different players
                players = ["aplay", "paplay", "mpg123", "ffplay"]
                for player in players:
                    try:
                        subprocess.run([player, file_path], check=True, 
                                     stdout=subprocess.DEVNULL, 
                                     stderr=subprocess.DEVNULL)
                        break
                    except (subprocess.CalledProcessError, FileNotFoundError):
                        continue
                else:
                    raise Exception("No audio player found")
            
            # Wait for audio to finish (approximate)
            import os
            file_size = os.path.getsize(file_path)
            estimated_duration = file_size / 16000  # Rough estimate
            await asyncio.sleep(min(estimated_duration, 10))  # Cap at 10 seconds
            
        except Exception as e:
            logger.error(f"Audio playback failed: {e}")
    
    async def stop_speaking(self):
        """Stop current speech"""
        try:
            if not self.is_speaking:
                return
            
            if self.provider == "pyttsx3" and self.engine:
                self.engine.stop()
            
            self.is_speaking = False
            logger.info("Speech stopped")
            
        except Exception as e:
            logger.error(f"Failed to stop speech: {e}")
    
    def get_available_voices(self) -> List[dict]:
        """Get list of available voices"""
        try:
            voices = []
            
            if self.provider == "pyttsx3" and self.engine:
                pyttsx3_voices = self.engine.getProperty('voices')
                for voice in pyttsx3_voices:
                    voices.append({
                        "id": voice.id,
                        "name": voice.name,
                        "language": getattr(voice, 'languages', ['unknown'])[0],
                        "gender": "unknown"
                    })
            
            elif self.provider == "edge":
                # Common Edge TTS voices
                edge_voices = [
                    {"id": "en-US-AriaNeural", "name": "Aria", "language": "en-US", "gender": "female"},
                    {"id": "en-US-JennyNeural", "name": "Jenny", "language": "en-US", "gender": "female"},
                    {"id": "en-US-GuyNeural", "name": "Guy", "language": "en-US", "gender": "male"},
                    {"id": "en-GB-SoniaNeural", "name": "Sonia", "language": "en-GB", "gender": "female"},
                    {"id": "en-AU-NatashaNeural", "name": "Natasha", "language": "en-AU", "gender": "female"}
                ]
                voices.extend(edge_voices)
            
            return voices
            
        except Exception as e:
            logger.error(f"Failed to get available voices: {e}")
            return []
    
    def set_voice(self, voice_id: str) -> bool:
        """Set the voice to use"""
        try:
            if self.provider == "pyttsx3" and self.engine:
                self.engine.setProperty('voice', voice_id)
                logger.info(f"Set pyttsx3 voice to: {voice_id}")
                return True
            
            elif self.provider == "edge":
                self.edge_voice = voice_id
                logger.info(f"Set Edge TTS voice to: {voice_id}")
                return True
            
            elif self.provider == "elevenlabs":
                self.elevenlabs_voice_id = voice_id
                logger.info(f"Set ElevenLabs voice to: {voice_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to set voice: {e}")
            return False
    
    def set_rate(self, rate: int) -> bool:
        """Set speech rate"""
        try:
            self.voice_rate = rate
            
            if self.provider == "pyttsx3" and self.engine:
                self.engine.setProperty('rate', rate)
            
            logger.info(f"Set speech rate to: {rate}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set speech rate: {e}")
            return False
    
    def set_volume(self, volume: float) -> bool:
        """Set speech volume (0.0 to 1.0)"""
        try:
            self.voice_volume = max(0.0, min(1.0, volume))
            
            if self.provider == "pyttsx3" and self.engine:
                self.engine.setProperty('volume', self.voice_volume)
            
            logger.info(f"Set speech volume to: {self.voice_volume}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set speech volume: {e}")
            return False
    
    async def test_speech(self) -> bool:
        """Test TTS functionality"""
        try:
            test_text = "Hello, this is a test of the text to speech system."
            logger.info("Testing TTS...")
            
            success = await self.speak(test_text)
            
            if success:
                logger.info("TTS test successful")
            else:
                logger.error("TTS test failed")
            
            return success
            
        except Exception as e:
            logger.error(f"TTS test failed: {e}")
            return False
    
    def get_status(self) -> dict:
        """Get TTS status information"""
        return {
            "provider": self.provider,
            "is_speaking": self.is_speaking,
            "voice_rate": self.voice_rate,
            "voice_volume": self.voice_volume,
            "available_voices": len(self.get_available_voices())
        }
