# Jarvis Environment Variables
# Copy this file to .env and fill in your API keys

# Required API Keys
GROQ_API_KEY=your_groq_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Optional API Keys
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
GOOGLE_CLOUD_API_KEY=your_google_cloud_api_key_here
AZURE_SPEECH_KEY=your_azure_speech_key_here

# Database Configuration
CHROMA_DB_PATH=./data/memory
CHROMA_DB_HOST=localhost
CHROMA_DB_PORT=8001

# Application Settings
DEBUG=true
LOG_LEVEL=INFO
SAFE_MODE=false

# Voice Settings
WAKE_WORD=jarvis
VOICE_ENABLED=true
STT_PROVIDER=whisper
TTS_PROVIDER=pyttsx3

# Docker Settings
DOCKER_NETWORK=jarvis_network
DOCKER_COMPOSE_PROJECT=jarvis
