"""
Skill Manager for Jarvis AI Assistant
Manages loading, execution, and coordination of skills
"""

import os
import importlib
import inspect
from pathlib import Path
from typing import Dict, List, Optional, Any, Type
from .base_skill import BaseSkill, SkillResult
from core.config import config
from core.logger import get_logger

logger = get_logger("skill_manager")


class SkillManager:
    """Manages all skills in the system"""
    
    def __init__(self):
        self.skills: Dict[str, BaseSkill] = {}
        self.skills_directory = Path(config.skills.skills_directory)
        self.auto_load = config.skills.auto_load
        
        if self.auto_load:
            self.load_all_skills()
    
    def load_all_skills(self):
        """Load all skills from the skills directory"""
        try:
            logger.info(f"Loading skills from: {self.skills_directory}")
            
            if not self.skills_directory.exists():
                self.skills_directory.mkdir(parents=True, exist_ok=True)
                logger.info(f"Created skills directory: {self.skills_directory}")
                self._create_example_skills()
            
            # Load skills from Python files
            for skill_file in self.skills_directory.glob("*.py"):
                if skill_file.name.startswith("__"):
                    continue
                
                try:
                    self.load_skill_from_file(skill_file)
                except Exception as e:
                    logger.error(f"Failed to load skill from {skill_file}: {e}")
            
            logger.info(f"Loaded {len(self.skills)} skills")
            
        except Exception as e:
            logger.error(f"Failed to load skills: {e}")
    
    def load_skill_from_file(self, file_path: Path) -> bool:
        """Load a skill from a Python file"""
        try:
            # Import the module
            spec = importlib.util.spec_from_file_location(file_path.stem, file_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Find skill classes in the module
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    issubclass(obj, BaseSkill) and 
                    obj != BaseSkill):
                    
                    # Create skill instance
                    skill_instance = obj()
                    
                    # Check dependencies
                    missing_deps = skill_instance.check_dependencies()
                    if missing_deps:
                        logger.warning(f"Skill {skill_instance.name} missing dependencies: {missing_deps}")
                        continue
                    
                    # Initialize skill
                    if await skill_instance.initialize():
                        self.skills[skill_instance.name] = skill_instance
                        logger.info(f"Loaded skill: {skill_instance.name}")
                        return True
                    else:
                        logger.error(f"Failed to initialize skill: {skill_instance.name}")
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to load skill from {file_path}: {e}")
            return False
    
    def _create_example_skills(self):
        """Create example skills for demonstration"""
        try:
            # Create a simple calculator skill
            calculator_skill = '''
"""
Calculator Skill for Jarvis AI Assistant
"""

import re
import math
from typing import Dict, Optional, Any
from skills.base_skill import BaseSkill, SkillResult


class CalculatorSkill(BaseSkill):
    """Simple calculator skill"""
    
    def __init__(self):
        super().__init__(
            name="calculator",
            description="Performs basic mathematical calculations"
        )
        self.keywords = ["calculate", "math", "add", "subtract", "multiply", "divide", "plus", "minus"]
    
    def can_handle(self, command: str, parameters: Optional[Dict[str, Any]] = None) -> bool:
        """Check if this is a math calculation request"""
        command_lower = command.lower()
        return any(keyword in command_lower for keyword in self.keywords)
    
    async def execute(
        self,
        command: str,
        parameters: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> SkillResult:
        """Execute calculation"""
        try:
            # Extract mathematical expression
            expression = self._extract_expression(command)
            
            if not expression:
                return SkillResult(
                    success=False,
                    error="No mathematical expression found",
                    message="Please provide a mathematical expression to calculate"
                )
            
            # Evaluate expression safely
            result = self._safe_eval(expression)
            
            return SkillResult(
                success=True,
                data={"expression": expression, "result": result},
                message=f"{expression} = {result}"
            )
            
        except Exception as e:
            return SkillResult(
                success=False,
                error=str(e),
                message="Calculation failed"
            )
    
    def _extract_expression(self, command: str) -> str:
        """Extract mathematical expression from command"""
        # Remove common words
        cleaned = command.lower()
        for word in ["calculate", "what is", "compute", "solve"]:
            cleaned = cleaned.replace(word, "")
        
        # Replace words with operators
        replacements = {
            "plus": "+",
            "add": "+",
            "minus": "-",
            "subtract": "-",
            "times": "*",
            "multiply": "*",
            "divided by": "/",
            "divide": "/",
            "power": "**",
            "squared": "**2",
            "cubed": "**3"
        }
        
        for word, operator in replacements.items():
            cleaned = cleaned.replace(word, operator)
        
        # Extract numbers and operators
        expression = re.sub(r'[^0-9+\\-*/.() ]', '', cleaned)
        expression = re.sub(r'\\s+', '', expression)
        
        return expression.strip()
    
    def _safe_eval(self, expression: str) -> float:
        """Safely evaluate mathematical expression"""
        # Only allow safe operations
        allowed_names = {
            "abs": abs,
            "round": round,
            "min": min,
            "max": max,
            "sum": sum,
            "pow": pow,
            "sqrt": math.sqrt,
            "sin": math.sin,
            "cos": math.cos,
            "tan": math.tan,
            "pi": math.pi,
            "e": math.e
        }
        
        # Compile and evaluate
        code = compile(expression, "<string>", "eval")
        
        # Check for unsafe operations
        for name in code.co_names:
            if name not in allowed_names:
                raise ValueError(f"Unsafe operation: {name}")
        
        return eval(code, {"__builtins__": {}}, allowed_names)
'''
            
            # Write calculator skill file
            calc_file = self.skills_directory / "calculator_skill.py"
            with open(calc_file, 'w') as f:
                f.write(calculator_skill)
            
            logger.info("Created example calculator skill")
            
        except Exception as e:
            logger.error(f"Failed to create example skills: {e}")
    
    async def execute_skill(
        self,
        command: str,
        parameters: Optional[Dict[str, Any]] = None,
        preferred_skill: Optional[str] = None
    ) -> SkillResult:
        """Execute a command using the most appropriate skill"""
        try:
            # Find suitable skill
            if preferred_skill and preferred_skill in self.skills:
                skill = self.skills[preferred_skill]
                if skill.enabled and skill.can_handle(command, parameters):
                    return await skill.execute(command, parameters)
            
            # Find any skill that can handle the command
            for skill_name, skill in self.skills.items():
                if skill.enabled and skill.can_handle(command, parameters):
                    logger.info(f"Executing command with skill: {skill_name}")
                    return await skill.execute(command, parameters)
            
            return SkillResult(
                success=False,
                error="No suitable skill found",
                message="No skill can handle this command"
            )
            
        except Exception as e:
            logger.error(f"Skill execution failed: {e}")
            return SkillResult(
                success=False,
                error=str(e),
                message="Skill execution failed"
            )
    
    def get_skill_recommendations(self, command: str) -> List[Dict[str, Any]]:
        """Get skill recommendations for a command"""
        recommendations = []
        
        for skill_name, skill in self.skills.items():
            if not skill.enabled:
                continue
            
            can_handle = skill.can_handle(command)
            confidence = self._calculate_confidence(skill, command)
            
            recommendations.append({
                "skill": skill_name,
                "description": skill.description,
                "can_handle": can_handle,
                "confidence": confidence,
                "keywords": skill.keywords
            })
        
        # Sort by confidence
        recommendations.sort(key=lambda x: x["confidence"], reverse=True)
        
        return recommendations
    
    def _calculate_confidence(self, skill: BaseSkill, command: str) -> float:
        """Calculate confidence score for skill handling command"""
        command_lower = command.lower()
        
        # Count keyword matches
        keyword_matches = sum(1 for keyword in skill.keywords if keyword in command_lower)
        
        if not skill.keywords:
            return 0.1  # Low confidence for skills without keywords
        
        # Calculate confidence based on keyword matches
        confidence = keyword_matches / len(skill.keywords)
        
        # Boost confidence if skill can handle the command
        if skill.can_handle(command):
            confidence += 0.5
        
        return min(1.0, confidence)
    
    def list_skills(self) -> List[Dict[str, Any]]:
        """List all available skills"""
        return [skill.get_info() for skill in self.skills.values()]
    
    def get_skill_info(self, skill_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific skill"""
        if skill_name in self.skills:
            return self.skills[skill_name].get_info()
        return None
    
    def get_skill_help(self, skill_name: str) -> Optional[str]:
        """Get help text for a specific skill"""
        if skill_name in self.skills:
            return self.skills[skill_name].get_help()
        return None
    
    def enable_skill(self, skill_name: str) -> bool:
        """Enable a skill"""
        if skill_name in self.skills:
            self.skills[skill_name].enable()
            return True
        return False
    
    def disable_skill(self, skill_name: str) -> bool:
        """Disable a skill"""
        if skill_name in self.skills:
            self.skills[skill_name].disable()
            return True
        return False
    
    def reload_skill(self, skill_name: str) -> bool:
        """Reload a skill"""
        try:
            if skill_name in self.skills:
                # Cleanup old skill
                await self.skills[skill_name].cleanup()
                del self.skills[skill_name]
            
            # Find and reload skill file
            skill_file = self.skills_directory / f"{skill_name}_skill.py"
            if skill_file.exists():
                return self.load_skill_from_file(skill_file)
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to reload skill {skill_name}: {e}")
            return False
    
    def install_skill(self, skill_path: str) -> bool:
        """Install a skill from file or URL"""
        try:
            # For now, just copy file to skills directory
            source_path = Path(skill_path)
            
            if source_path.exists():
                dest_path = self.skills_directory / source_path.name
                
                # Copy file
                import shutil
                shutil.copy2(source_path, dest_path)
                
                # Load the skill
                return self.load_skill_from_file(dest_path)
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to install skill from {skill_path}: {e}")
            return False
    
    def uninstall_skill(self, skill_name: str) -> bool:
        """Uninstall a skill"""
        try:
            if skill_name in self.skills:
                # Cleanup skill
                await self.skills[skill_name].cleanup()
                del self.skills[skill_name]
                
                # Remove skill file
                skill_file = self.skills_directory / f"{skill_name}_skill.py"
                if skill_file.exists():
                    skill_file.unlink()
                
                logger.info(f"Uninstalled skill: {skill_name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to uninstall skill {skill_name}: {e}")
            return False
    
    async def cleanup_all_skills(self):
        """Cleanup all skills"""
        for skill in self.skills.values():
            try:
                await skill.cleanup()
            except Exception as e:
                logger.error(f"Failed to cleanup skill {skill.name}: {e}")


# Global skill manager instance
skill_manager = SkillManager()
