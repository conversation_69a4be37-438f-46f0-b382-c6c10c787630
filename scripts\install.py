#!/usr/bin/env python3
"""
Jarvis AI Assistant Installation Script
Automated setup and configuration
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
import shutil
import urllib.request
import zipfile
import tempfile

def print_banner():
    """Print installation banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                🤖 Jarvis AI Assistant                        ║
    ║                   Installation Script                        ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 11):
        print("❌ Python 3.11 or higher is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        sys.exit(1)
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")

def check_system_requirements():
    """Check system requirements"""
    print("🔍 Checking system requirements...")
    
    system = platform.system()
    print(f"   Operating System: {system}")
    print(f"   Architecture: {platform.machine()}")
    
    # Check available disk space
    if system == "Windows":
        import shutil
        total, used, free = shutil.disk_usage("C:\\")
    else:
        total, used, free = shutil.disk_usage("/")
    
    free_gb = free // (1024**3)
    if free_gb < 2:
        print(f"⚠️  Low disk space: {free_gb}GB available (2GB+ recommended)")
    else:
        print(f"✅ Disk space: {free_gb}GB available")

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install requirements
        requirements_file = Path("requirements.txt")
        if requirements_file.exists():
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ])
            print("✅ Python dependencies installed successfully")
        else:
            print("⚠️  requirements.txt not found, installing core dependencies...")
            core_deps = [
                "fastapi>=0.104.1",
                "uvicorn>=0.24.0",
                "pydantic>=2.5.0",
                "python-dotenv>=1.0.0",
                "pyyaml>=6.0.1",
                "groq>=0.4.1",
                "chromadb>=0.4.18",
                "sentence-transformers>=2.2.2",
                "click>=8.1.7",
                "rich>=13.7.0"
            ]
            
            for dep in core_deps:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            
            print("✅ Core dependencies installed")
    
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        sys.exit(1)

def install_optional_dependencies():
    """Install optional dependencies with user choice"""
    print("\n🔧 Optional Components:")
    
    # Voice components
    install_voice = input("   Install voice control components? (y/N): ").lower().startswith('y')
    if install_voice:
        try:
            voice_deps = [
                "pyaudio>=0.2.11",
                "speech-recognition>=3.10.0", 
                "pyttsx3>=2.90",
                "whisper>=1.1.10"
            ]
            
            for dep in voice_deps:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            
            print("✅ Voice components installed")
        except subprocess.CalledProcessError:
            print("⚠️  Voice components installation failed (may require system audio libraries)")
    
    # Browser automation
    install_browser = input("   Install browser automation components? (y/N): ").lower().startswith('y')
    if install_browser:
        try:
            browser_deps = [
                "playwright>=1.40.0",
                "selenium>=4.15.2",
                "beautifulsoup4>=4.12.2"
            ]
            
            for dep in browser_deps:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            
            # Install Playwright browsers
            subprocess.check_call([sys.executable, "-m", "playwright", "install", "chromium"])
            
            print("✅ Browser automation components installed")
        except subprocess.CalledProcessError:
            print("⚠️  Browser components installation failed")

def setup_configuration():
    """Set up configuration files"""
    print("⚙️  Setting up configuration...")
    
    # Create .env file if it doesn't exist
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        shutil.copy2(env_example, env_file)
        print("✅ Created .env file from template")
        print("   Please edit .env with your API keys")
    
    # Create data directories
    directories = [
        "data",
        "data/memory", 
        "data/audio",
        "logs",
        "skills"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Created data directories")

def setup_api_keys():
    """Help user set up API keys"""
    print("\n🔑 API Key Setup:")
    print("   Jarvis requires at least one LLM provider API key")
    # Groq (Recommended - Fast and Free)")
    print("   1. Visit: https://console.groq.com/")
    print("   2. Create account and get API key")
    print("   3. Add to .env: GROQ_API_KEY=your_key_here")
    
    print("\n   # OpenAI (Optional)")
    print("   1. Visit: https://platform.openai.com/")
    print("   2. Create account and get API key") 
    print("   3. Add to .env: OPENAI_API_KEY=your_key_here")
    
    # Check if user wants to set up keys now
    setup_now = input("\n   Set up Groq API key now? (y/N): ").lower().startswith('y')
    if setup_now:
        api_key = input("   Enter your Groq API key: ").strip()
        if api_key:
            env_file = Path(".env")
            
            # Read existing .env content
            env_content = ""
            if env_file.exists():
                env_content = env_file.read_text()
            
            # Update or add Groq API key
            if "GROQ_API_KEY=" in env_content:
                # Replace existing key
                lines = env_content.split('\n')
                for i, line in enumerate(lines):
                    if line.startswith("GROQ_API_KEY="):
                        lines[i] = f"GROQ_API_KEY={api_key}"
                        break
                env_content = '\n'.join(lines)
            else:
                # Add new key
                env_content += f"\nGROQ_API_KEY={api_key}\n"
            
            env_file.write_text(env_content)
            print("✅ Groq API key saved to .env")

def test_installation():
    """Test the installation"""
    print("\n🧪 Testing installation...")
    
    try:
        # Test imports
        print("   Testing core imports...")
        import core.config
        import core.llm
        import agents.agent_dispatcher
        print("✅ Core imports successful")
        
        # Test configuration
        print("   Testing configuration...")
        from core.config import config
        config.validate_config()
        print("✅ Configuration valid")
        
        print("✅ Installation test passed!")
        
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        print("   Please check the error and try again")

def print_next_steps():
    """Print next steps for the user"""
    print("\n🎉 Installation Complete!")
    print("\n📋 Next Steps:")
    print("   1. Edit .env file with your API keys")
    print("   2. Test the installation:")
    print("      python cli.py test")
    print("   3. Start Jarvis:")
    print("      python cli.py start")
    print("   4. Or start with voice:")
    print("      python cli.py start --voice")
    print("   5. Or start web server:")
    print("      python cli.py server")
    
    print("\n📚 Documentation:")
    print("   - README.md for detailed usage")
    print("   - config.yaml for configuration options")
    print("   - Check logs/ directory for troubleshooting")
    
    print("\n🆘 Need Help?")
    print("   - Run: python cli.py --help")
    print("   - Check the README.md file")
    print("   - Visit the project repository")

def main():
    """Main installation function"""
    print_banner()
    
    try:
        check_python_version()
        check_system_requirements()
        install_dependencies()
        install_optional_dependencies()
        setup_configuration()
        setup_api_keys()
        test_installation()
        print_next_steps()
        
    except KeyboardInterrupt:
        print("\n\n❌ Installation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Installation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
