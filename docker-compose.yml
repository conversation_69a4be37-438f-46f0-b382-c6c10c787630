version: '3.8'

services:
  jarvis-main:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: jarvis-main
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./skills:/app/skills
      - ./logs:/app/logs
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - GROQ_API_KEY=${GROQ_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - CHROMA_DB_HOST=jarvis-memory
      - CHROMA_DB_PORT=8001
    depends_on:
      - jarvis-memory
    networks:
      - jarvis_network
    restart: unless-stopped

  jarvis-memory:
    image: chromadb/chroma:latest
    container_name: jarvis-memory
    ports:
      - "8001:8000"
    volumes:
      - ./data/memory:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    networks:
      - jarvis_network
    restart: unless-stopped

  jarvis-voice:
    build:
      context: .
      dockerfile: docker/Dockerfile.voice
    container_name: jarvis-voice
    ports:
      - "8002:8002"
    volumes:
      - ./data/audio:/app/audio
    devices:
      - /dev/snd:/dev/snd
    environment:
      - PULSE_RUNTIME_PATH=/var/run/pulse
    networks:
      - jarvis_network
    restart: unless-stopped
    profiles:
      - voice

networks:
  jarvis_network:
    driver: bridge

volumes:
  jarvis_data:
  jarvis_memory:
  jarvis_logs:
