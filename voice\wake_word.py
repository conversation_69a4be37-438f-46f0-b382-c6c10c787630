"""
Wake Word Detection for Jarvis AI Assistant
"""

import asyncio
import threading
import struct
from typing import Optional, Callable
import pyaudio
import numpy as np
from core.config import config
from core.logger import get_logger

logger = get_logger("wake_word")


class WakeWordDetector:
    """Detects wake words to activate voice control"""
    
    def __init__(self):
        self.wake_word = config.voice.wake_word.lower()
        self.is_listening = False
        self.callback_function: Optional[Callable] = None
        
        # Audio settings
        self.sample_rate = 16000
        self.chunk_size = 1024
        self.channels = 1
        self.format = pyaudio.paInt16
        
        # Detection settings
        self.energy_threshold = 300
        self.silence_threshold = 0.5  # seconds of silence before reset
        self.max_phrase_length = 3.0  # maximum phrase length in seconds
        
        # Audio processing
        self.audio_buffer = []
        self.silence_counter = 0
        self.is_recording_phrase = False
        
        self.pyaudio_instance = None
        self.stream = None
        
        self.initialize()
    
    def initialize(self):
        """Initialize wake word detection"""
        try:
            self.pyaudio_instance = pyaudio.PyAudio()
            logger.info(f"Wake word detector initialized for: '{self.wake_word}'")
            
        except Exception as e:
            logger.error(f"Wake word detector initialization failed: {e}")
            raise
    
    async def start_detection(self, callback: Callable[[], None]):
        """Start wake word detection"""
        if self.is_listening:
            logger.warning("Wake word detection already running")
            return
        
        self.callback_function = callback
        self.is_listening = True
        
        logger.info(f"Starting wake word detection for: '{self.wake_word}'")
        
        try:
            # Open audio stream
            self.stream = self.pyaudio_instance.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size,
                stream_callback=self._audio_callback
            )
            
            self.stream.start_stream()
            logger.info("Wake word detection started")
            
        except Exception as e:
            logger.error(f"Failed to start wake word detection: {e}")
            self.is_listening = False
            raise
    
    def stop_detection(self):
        """Stop wake word detection"""
        if not self.is_listening:
            return
        
        self.is_listening = False
        
        try:
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            
            self.audio_buffer.clear()
            self.silence_counter = 0
            self.is_recording_phrase = False
            
            logger.info("Wake word detection stopped")
            
        except Exception as e:
            logger.error(f"Error stopping wake word detection: {e}")
    
    def _audio_callback(self, in_data, frame_count, time_info, status):
        """Audio stream callback for real-time processing"""
        if not self.is_listening:
            return (None, pyaudio.paComplete)
        
        try:
            # Convert audio data to numpy array
            audio_data = np.frombuffer(in_data, dtype=np.int16)
            
            # Calculate energy level
            energy = np.sqrt(np.mean(audio_data**2))
            
            # Process audio for wake word detection
            self._process_audio_chunk(audio_data, energy)
            
        except Exception as e:
            logger.error(f"Audio callback error: {e}")
        
        return (in_data, pyaudio.paContinue)
    
    def _process_audio_chunk(self, audio_data: np.ndarray, energy: float):
        """Process audio chunk for wake word detection"""
        try:
            # Check if audio has sufficient energy (voice activity detection)
            if energy > self.energy_threshold:
                if not self.is_recording_phrase:
                    # Start recording a new phrase
                    self.is_recording_phrase = True
                    self.audio_buffer = []
                    self.silence_counter = 0
                    logger.debug("Started recording phrase")
                
                # Add audio to buffer
                self.audio_buffer.extend(audio_data)
                self.silence_counter = 0
                
                # Check buffer length limit
                buffer_duration = len(self.audio_buffer) / self.sample_rate
                if buffer_duration > self.max_phrase_length:
                    self._reset_buffer()
            
            else:
                # Low energy (silence)
                if self.is_recording_phrase:
                    self.silence_counter += self.chunk_size / self.sample_rate
                    
                    # If enough silence, process the recorded phrase
                    if self.silence_counter >= self.silence_threshold:
                        self._process_recorded_phrase()
                        self._reset_buffer()
        
        except Exception as e:
            logger.error(f"Audio chunk processing error: {e}")
    
    def _process_recorded_phrase(self):
        """Process the recorded phrase for wake word detection"""
        try:
            if not self.audio_buffer:
                return
            
            # Convert buffer to audio format for recognition
            audio_array = np.array(self.audio_buffer, dtype=np.int16)
            
            # Simple wake word detection using audio features
            # This is a basic implementation - in production, you'd use
            # more sophisticated methods like:
            # - Porcupine wake word engine
            # - Custom trained models
            # - Speech recognition with keyword spotting
            
            detected = self._simple_wake_word_detection(audio_array)
            
            if detected:
                logger.info(f"Wake word '{self.wake_word}' detected!")
                if self.callback_function:
                    # Run callback in a separate thread to avoid blocking audio
                    threading.Thread(
                        target=self.callback_function,
                        daemon=True
                    ).start()
        
        except Exception as e:
            logger.error(f"Phrase processing error: {e}")
    
    def _simple_wake_word_detection(self, audio_data: np.ndarray) -> bool:
        """Simple wake word detection based on audio characteristics"""
        try:
            # This is a placeholder implementation
            # In a real system, you would:
            # 1. Use speech recognition to convert audio to text
            # 2. Check if the text contains the wake word
            # 3. Use acoustic models trained for the specific wake word
            
            # For now, we'll use a simple energy-based detection
            # that triggers on any speech-like audio pattern
            
            # Calculate audio features
            duration = len(audio_data) / self.sample_rate
            energy = np.sqrt(np.mean(audio_data**2))
            
            # Simple heuristics for wake word detection
            # These would be replaced with proper ML models
            min_duration = 0.5  # Minimum duration for wake word
            max_duration = 2.0  # Maximum duration for wake word
            min_energy = 500    # Minimum energy level
            
            if (min_duration <= duration <= max_duration and 
                energy >= min_energy):
                
                # Additional checks could include:
                # - Spectral analysis
                # - Phoneme detection
                # - Neural network classification
                
                # For demo purposes, detect based on audio characteristics
                # In practice, integrate with Porcupine or similar
                return self._check_audio_pattern(audio_data)
            
            return False
            
        except Exception as e:
            logger.error(f"Wake word detection error: {e}")
            return False
    
    def _check_audio_pattern(self, audio_data: np.ndarray) -> bool:
        """Check audio pattern for wake word characteristics"""
        try:
            # This is a very basic pattern matching
            # Replace with proper wake word detection library
            
            # Calculate zero crossing rate (indicator of speech)
            zero_crossings = np.sum(np.diff(np.sign(audio_data)) != 0)
            zcr = zero_crossings / len(audio_data)
            
            # Calculate spectral centroid (brightness of sound)
            fft = np.fft.fft(audio_data)
            magnitude = np.abs(fft)
            freqs = np.fft.fftfreq(len(fft), 1/self.sample_rate)
            
            # Only use positive frequencies
            positive_freqs = freqs[:len(freqs)//2]
            positive_magnitude = magnitude[:len(magnitude)//2]
            
            if np.sum(positive_magnitude) > 0:
                spectral_centroid = np.sum(positive_freqs * positive_magnitude) / np.sum(positive_magnitude)
            else:
                spectral_centroid = 0
            
            # Heuristic thresholds (would be learned from training data)
            zcr_threshold = 0.1
            centroid_min = 200
            centroid_max = 3000
            
            speech_like = (zcr > zcr_threshold and 
                          centroid_min < spectral_centroid < centroid_max)
            
            if speech_like:
                logger.debug(f"Speech-like audio detected (ZCR: {zcr:.3f}, Centroid: {spectral_centroid:.1f})")
                # For demo, randomly trigger to simulate wake word detection
                # In practice, this would be replaced with proper detection
                import random
                return random.random() < 0.3  # 30% chance to simulate detection
            
            return False
            
        except Exception as e:
            logger.error(f"Audio pattern check error: {e}")
            return False
    
    def _reset_buffer(self):
        """Reset audio buffer and recording state"""
        self.audio_buffer.clear()
        self.silence_counter = 0
        self.is_recording_phrase = False
        logger.debug("Audio buffer reset")
    
    def set_sensitivity(self, threshold: float):
        """Set detection sensitivity (energy threshold)"""
        self.energy_threshold = max(50, min(1000, threshold))
        logger.info(f"Set wake word sensitivity to: {self.energy_threshold}")
    
    def set_wake_word(self, wake_word: str):
        """Set the wake word to detect"""
        self.wake_word = wake_word.lower().strip()
        logger.info(f"Set wake word to: '{self.wake_word}'")
    
    def get_status(self) -> dict:
        """Get wake word detector status"""
        return {
            "wake_word": self.wake_word,
            "is_listening": self.is_listening,
            "energy_threshold": self.energy_threshold,
            "is_recording_phrase": self.is_recording_phrase,
            "buffer_size": len(self.audio_buffer)
        }
    
    def test_detection(self) -> bool:
        """Test wake word detection functionality"""
        try:
            logger.info("Testing wake word detection...")
            logger.info(f"Say '{self.wake_word}' to test detection")
            
            # This would typically involve:
            # 1. Recording a test phrase
            # 2. Processing it through the detection pipeline
            # 3. Verifying the result
            
            # For now, just verify the audio system is working
            if self.pyaudio_instance:
                device_count = self.pyaudio_instance.get_device_count()
                logger.info(f"Audio system working: {device_count} devices available")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Wake word detection test failed: {e}")
            return False
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        try:
            self.stop_detection()
            if self.pyaudio_instance:
                self.pyaudio_instance.terminate()
        except:
            pass
