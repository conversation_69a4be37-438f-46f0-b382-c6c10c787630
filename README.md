# 🤖 Jarvis AI Assistant

A powerful, multi-agent AI assistant with voice control, system integration, and extensible skills.

## ✨ Features

- **Multi-Agent Architecture**: Specialized agents for different tasks (development, research, system control, etc.)
- **Voice Control**: Speech-to-text and text-to-speech with wake word detection
- **Extensible Skills**: Plugin-based skill system for adding new capabilities
- **Memory System**: Vector-based memory for context and learning
- **Web Interface**: Modern web UI for interaction
- **System Integration**: Control applications, files, and system operations
- **LLM Support**: Multiple LLM providers (Groq, OpenAI)

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Windows/macOS/Linux
- Microphone (for voice features)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd jarvis
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Set up environment variables**
```bash
cp .env.example .env
# Edit .env with your API keys
```

4. **Install Playwright browsers** (for web automation)
```bash
playwright install chromium
```

### Configuration

Edit `config.yaml` to customize settings:

```yaml
# API Keys (set in .env file)
api_keys:
  groq_api_key: "${GROQ_API_KEY}"
  openai_api_key: "${OPENAI_API_KEY}"

# Voice settings
voice:
  enabled: true
  wake_word: "jarvis"
  stt_provider: "whisper"
  tts_provider: "pyttsx3"

# Agent settings
agents:
  max_concurrent: 5
  available:
    - planner
    - developer
    - shell
    - file_manager
    - browser
    - system_control
    - research
```

## 🎯 Usage

### Interactive Mode

Start Jarvis in interactive chat mode:

```bash
python cli.py start
```

### Voice Mode

Start with voice control:

```bash
python cli.py start --voice
```

### Web Server

Start the web interface:

```bash
python cli.py server
```

Then open http://localhost:8000 in your browser.

### Docker

Run with Docker:

```bash
docker-compose up
```

## 🧠 Agents

Jarvis includes several specialized agents:

### 🎯 Planner Agent
- Breaks down complex tasks into steps
- Creates execution plans
- Coordinates multi-agent workflows

### 💻 Developer Agent
- Writes and debugs code
- Supports multiple programming languages
- Code review and testing

### 🖥️ Shell Agent
- Executes command line operations
- Script running and process management
- Safe command validation

### 📁 File Manager Agent
- File and directory operations
- Content analysis and search
- Safe file manipulation

### 🌐 Browser Agent
- Web scraping and automation
- Form filling and interaction
- Screenshot capture

### ⚙️ System Control Agent
- Application control
- Mouse and keyboard automation
- System monitoring

### 🔍 Research Agent
- Information gathering
- Fact checking and analysis
- Report generation

## 🛠️ Skills

Skills are modular capabilities that can be easily added:

### Built-in Skills
- **Calculator**: Mathematical calculations
- **Weather**: Weather information (when API configured)
- **Timer**: Set timers and reminders

### Creating Custom Skills

```python
from skills.base_skill import BaseSkill, SkillResult

class MySkill(BaseSkill):
    def __init__(self):
        super().__init__(
            name="my_skill",
            description="Description of what this skill does"
        )
        self.keywords = ["keyword1", "keyword2"]
    
    def can_handle(self, command: str, parameters=None) -> bool:
        return any(keyword in command.lower() for keyword in self.keywords)
    
    async def execute(self, command: str, parameters=None, **kwargs) -> SkillResult:
        # Implement skill logic here
        return SkillResult(
            success=True,
            message="Skill executed successfully",
            data={"result": "some data"}
        )
```

## 🎤 Voice Control

### Setup

1. **Configure microphone**: Ensure your microphone is working
2. **Calibrate**: Run `python cli.py test` to calibrate voice recognition
3. **Set wake word**: Default is "jarvis", configurable in `config.yaml`

### Usage

1. Start voice mode: `python cli.py start --voice`
2. Say the wake word: "Jarvis"
3. Give your command when prompted
4. Jarvis will respond with voice and text

### Voice Commands

- "Jarvis, what's the weather like?"
- "Jarvis, create a Python script"
- "Jarvis, open notepad"
- "Jarvis, search for information about AI"

## 🔧 Configuration

### Environment Variables

```bash
# Required
GROQ_API_KEY=your_groq_api_key_here

# Optional
OPENAI_API_KEY=your_openai_api_key_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
```

### Voice Settings

```yaml
voice:
  enabled: true
  wake_word: "jarvis"
  stt_provider: "whisper"  # whisper, google
  tts_provider: "pyttsx3"  # pyttsx3, elevenlabs, edge
  language: "en-US"
  voice_rate: 200
  voice_volume: 0.9
```

### Memory Settings

```yaml
memory:
  provider: "chromadb"
  persist_directory: "./data/memory"
  collection_name: "jarvis_memory"
  embedding_model: "all-MiniLM-L6-v2"
  max_memory_items: 10000
```

## 🔒 Security

### Safe Mode

Enable safe mode to restrict dangerous operations:

```yaml
system:
  safe_mode: true
  allow_system_commands: false
  restricted_commands:
    - "rm -rf"
    - "del /f /s /q"
    - "format"
    - "shutdown"
```

### Command Validation

All system commands are validated before execution:
- Restricted command filtering
- Path validation
- Permission checks

## 📊 Monitoring

### Status Check

```bash
python cli.py test
```

### Web Dashboard

Access the web interface at http://localhost:8000 for:
- Real-time chat
- System status
- Agent monitoring
- Memory statistics

## 🐛 Troubleshooting

### Common Issues

1. **Voice not working**
   - Check microphone permissions
   - Run voice calibration: `voice calibrate` in interactive mode
   - Verify audio drivers

2. **API errors**
   - Verify API keys in `.env` file
   - Check internet connection
   - Validate API key permissions

3. **Memory issues**
   - Check disk space in `./data/memory`
   - Restart if ChromaDB issues persist

4. **Agent failures**
   - Check logs in `./logs/jarvis.log`
   - Verify dependencies are installed
   - Check system permissions

### Logs

Logs are stored in `./logs/jarvis.log` with different levels:
- INFO: General information
- WARNING: Non-critical issues
- ERROR: Error conditions
- DEBUG: Detailed debugging (when debug mode enabled)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your changes
4. Write tests
5. Submit a pull request

### Development Setup

```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
pytest

# Format code
black .
isort .

# Type checking
mypy .
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenAI for GPT models
- Groq for fast inference
- ChromaDB for vector storage
- Whisper for speech recognition
- FastAPI for web framework

## 📞 Support

- Create an issue for bug reports
- Join discussions for questions
- Check documentation for guides

---

**Made with ❤️ by the Jarvis AI Team**
