#!/usr/bin/env python3
"""
Jarvis AI Assistant - Quick Run Script
Simplified launcher for <PERSON>
"""

import os
import sys
import asyncio
import argparse
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def print_banner():
    """Print Jarvis banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                🤖 Jarvis AI Assistant                        ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """Check if basic requirements are met"""
    try:
        # Check if .env file exists
        env_file = project_root / ".env"
        if not env_file.exists():
            print("⚠️  .env file not found. Creating from template...")
            env_example = project_root / ".env.example"
            if env_example.exists():
                import shutil
                shutil.copy2(env_example, env_file)
                print("✅ Created .env file. Please add your API keys.")
            else:
                print("❌ .env.example not found. Please create .env manually.")
                return False
        
        # Check for API keys
        from dotenv import load_dotenv
        load_dotenv(env_file)
        
        groq_key = os.getenv('GROQ_API_KEY')
        openai_key = os.getenv('OPENAI_API_KEY')
        
        if not groq_key and not openai_key:
            print("⚠️  No API keys found in .env file.")
            print("   Please add at least GROQ_API_KEY or OPENAI_API_KEY")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("   Please run: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ Setup check failed: {e}")
        return False

async def run_interactive():
    """Run in interactive mode"""
    try:
        from cli import JarvisCLI
        
        jarvis_cli = JarvisCLI()
        
        if not await jarvis_cli.initialize():
            print("❌ Failed to initialize Jarvis")
            return
        
        try:
            await jarvis_cli.start_interactive_mode()
        finally:
            await jarvis_cli.shutdown()
            
    except Exception as e:
        print(f"❌ Interactive mode failed: {e}")

async def run_voice():
    """Run in voice mode"""
    try:
        from cli import JarvisCLI
        
        jarvis_cli = JarvisCLI()
        
        if not await jarvis_cli.initialize():
            print("❌ Failed to initialize Jarvis")
            return
        
        try:
            await jarvis_cli.start_voice_mode()
        finally:
            await jarvis_cli.shutdown()
            
    except Exception as e:
        print(f"❌ Voice mode failed: {e}")

async def run_server(host="localhost", port=8000):
    """Run web server"""
    try:
        from cli import JarvisCLI
        
        jarvis_cli = JarvisCLI()
        
        if not await jarvis_cli.initialize():
            print("❌ Failed to initialize Jarvis")
            return
        
        try:
            await jarvis_cli.start_server_mode(host, port)
        finally:
            await jarvis_cli.shutdown()
            
    except Exception as e:
        print(f"❌ Server mode failed: {e}")

async def run_test():
    """Run system tests"""
    try:
        from cli import JarvisCLI
        
        jarvis_cli = JarvisCLI()
        
        if not await jarvis_cli.initialize():
            print("❌ Failed to initialize Jarvis")
            return
        
        print("🧪 Running system tests...")
        
        # Test LLM
        try:
            from core.llm import llm_manager
            response = await llm_manager.generate("Say hello")
            print(f"✅ LLM test: {response[:50]}...")
        except Exception as e:
            print(f"❌ LLM test failed: {e}")
        
        # Test memory
        try:
            from core.memory import memory_manager
            await memory_manager.store_memory("Test memory", memory_type="test")
            print("✅ Memory test passed")
        except Exception as e:
            print(f"❌ Memory test failed: {e}")
        
        # Test agents
        try:
            from agents.agent_dispatcher import dispatcher
            result = await dispatcher.dispatch_task("test task")
            print(f"✅ Agent test: {result.message}")
        except Exception as e:
            print(f"❌ Agent test failed: {e}")
        
        await jarvis_cli.shutdown()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Jarvis AI Assistant Quick Launcher")
    parser.add_argument("mode", choices=["chat", "voice", "server", "test"], 
                       help="Run mode")
    parser.add_argument("--host", default="localhost", help="Server host (server mode)")
    parser.add_argument("--port", type=int, default=8000, help="Server port (server mode)")
    parser.add_argument("--no-banner", action="store_true", help="Skip banner")
    
    args = parser.parse_args()
    
    if not args.no_banner:
        print_banner()
    
    # Change to project directory
    os.chdir(project_root)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please fix the issues above.")
        sys.exit(1)
    
    # Run the selected mode
    try:
        if args.mode == "chat":
            print("🚀 Starting interactive chat mode...")
            asyncio.run(run_interactive())
        elif args.mode == "voice":
            print("🎤 Starting voice mode...")
            asyncio.run(run_voice())
        elif args.mode == "server":
            print(f"🌐 Starting web server on {args.host}:{args.port}...")
            asyncio.run(run_server(args.host, args.port))
        elif args.mode == "test":
            print("🧪 Running tests...")
            asyncio.run(run_test())
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
