"""
Voice Controller for Jarvis AI Assistant
Coordinates voice input/output and integrates with agents
"""

import asyncio
from typing import Optional, Callable, Dict, Any
from .stt import STTManager
from .tts import TTSManager
from .wake_word import WakeWordDetector
from core.config import config
from core.logger import get_logger

logger = get_logger("voice_controller")


class VoiceController:
    """Main voice control coordinator"""
    
    def __init__(self):
        self.enabled = config.voice.enabled
        self.stt_manager: Optional[STTManager] = None
        self.tts_manager: Optional[TTSManager] = None
        self.wake_word_detector: Optional[WakeWordDetector] = None
        
        self.is_active = False
        self.is_listening = False
        self.conversation_callback: Optional[Callable] = None
        
        # Voice interaction state
        self.waiting_for_input = False
        self.last_interaction_time = None
        
        if self.enabled:
            self.initialize()
    
    def initialize(self):
        """Initialize voice control components"""
        try:
            logger.info("Initializing voice controller...")
            
            # Initialize STT
            self.stt_manager = STTManager()
            logger.info("STT manager initialized")
            
            # Initialize TTS
            self.tts_manager = TTSManager()
            logger.info("TTS manager initialized")
            
            # Initialize wake word detector
            self.wake_word_detector = WakeWordDetector()
            logger.info("Wake word detector initialized")
            
            logger.info("Voice controller initialization complete")
            
        except Exception as e:
            logger.error(f"Voice controller initialization failed: {e}")
            self.enabled = False
            raise
    
    async def start(self, conversation_callback: Callable[[str], str]):
        """Start voice control system"""
        if not self.enabled:
            logger.warning("Voice control is disabled")
            return False
        
        if self.is_active:
            logger.warning("Voice control already active")
            return True
        
        try:
            self.conversation_callback = conversation_callback
            self.is_active = True
            
            # Start wake word detection
            await self.wake_word_detector.start_detection(self._on_wake_word_detected)
            
            # Welcome message
            await self.speak("Voice control activated. Say 'Jarvis' to begin.")
            
            logger.info("Voice control started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start voice control: {e}")
            self.is_active = False
            return False
    
    async def stop(self):
        """Stop voice control system"""
        if not self.is_active:
            return
        
        try:
            self.is_active = False
            self.is_listening = False
            
            # Stop components
            if self.wake_word_detector:
                self.wake_word_detector.stop_detection()
            
            if self.stt_manager:
                self.stt_manager.stop_continuous_listening()
            
            if self.tts_manager:
                await self.tts_manager.stop_speaking()
            
            logger.info("Voice control stopped")
            
        except Exception as e:
            logger.error(f"Error stopping voice control: {e}")
    
    def _on_wake_word_detected(self):
        """Handle wake word detection"""
        try:
            logger.info("Wake word detected, starting conversation")
            
            if not self.is_listening:
                # Start listening for user input
                asyncio.create_task(self._start_conversation())
            
        except Exception as e:
            logger.error(f"Wake word handling failed: {e}")
    
    async def _start_conversation(self):
        """Start a voice conversation"""
        try:
            if self.is_listening:
                return
            
            self.is_listening = True
            self.waiting_for_input = True
            
            # Acknowledge wake word
            await self.speak("Yes, how can I help you?")
            
            # Listen for user input
            user_input = await self.listen()
            
            if user_input:
                logger.info(f"User said: {user_input}")
                
                # Process with conversation callback
                if self.conversation_callback:
                    try:
                        response = await self._call_conversation_callback(user_input)
                        
                        if response:
                            await self.speak(response)
                        else:
                            await self.speak("I'm sorry, I didn't understand that.")
                    
                    except Exception as e:
                        logger.error(f"Conversation processing failed: {e}")
                        await self.speak("I encountered an error processing your request.")
                
            else:
                await self.speak("I didn't hear anything. Please try again.")
            
        except Exception as e:
            logger.error(f"Conversation failed: {e}")
            await self.speak("I'm sorry, there was an error.")
        
        finally:
            self.is_listening = False
            self.waiting_for_input = False
    
    async def _call_conversation_callback(self, user_input: str) -> Optional[str]:
        """Call the conversation callback function"""
        try:
            if asyncio.iscoroutinefunction(self.conversation_callback):
                return await self.conversation_callback(user_input)
            else:
                return self.conversation_callback(user_input)
        except Exception as e:
            logger.error(f"Conversation callback failed: {e}")
            return None
    
    async def speak(self, text: str, interrupt: bool = False) -> bool:
        """Speak text using TTS"""
        if not self.enabled or not self.tts_manager:
            logger.debug(f"TTS disabled, would say: {text}")
            return False
        
        try:
            return await self.tts_manager.speak(text, interrupt)
        except Exception as e:
            logger.error(f"Speech failed: {e}")
            return False
    
    async def listen(self, timeout: float = 10.0) -> Optional[str]:
        """Listen for speech input"""
        if not self.enabled or not self.stt_manager:
            logger.warning("STT not available")
            return None
        
        try:
            return await self.stt_manager.listen_once(timeout)
        except Exception as e:
            logger.error(f"Listening failed: {e}")
            return None
    
    async def start_continuous_listening(self, callback: Callable[[str], None]):
        """Start continuous listening mode"""
        if not self.enabled or not self.stt_manager:
            logger.warning("STT not available for continuous listening")
            return
        
        try:
            await self.stt_manager.start_continuous_listening(callback)
        except Exception as e:
            logger.error(f"Continuous listening failed: {e}")
    
    def stop_continuous_listening(self):
        """Stop continuous listening mode"""
        if self.stt_manager:
            self.stt_manager.stop_continuous_listening()
    
    async def process_voice_command(self, command: str) -> Optional[str]:
        """Process a voice command and return response"""
        try:
            if not command or not command.strip():
                return None
            
            command = command.strip().lower()
            
            # Handle built-in voice commands
            if command in ["stop", "quit", "exit", "goodbye"]:
                await self.speak("Goodbye!")
                await self.stop()
                return "Voice control stopped"
            
            elif command in ["hello", "hi", "hey"]:
                return "Hello! How can I assist you today?"
            
            elif "volume up" in command:
                current_volume = self.tts_manager.voice_volume
                new_volume = min(1.0, current_volume + 0.1)
                self.tts_manager.set_volume(new_volume)
                return f"Volume increased to {int(new_volume * 100)}%"
            
            elif "volume down" in command:
                current_volume = self.tts_manager.voice_volume
                new_volume = max(0.0, current_volume - 0.1)
                self.tts_manager.set_volume(new_volume)
                return f"Volume decreased to {int(new_volume * 100)}%"
            
            elif "speak faster" in command:
                current_rate = self.tts_manager.voice_rate
                new_rate = min(300, current_rate + 25)
                self.tts_manager.set_rate(new_rate)
                return "Speaking faster now"
            
            elif "speak slower" in command:
                current_rate = self.tts_manager.voice_rate
                new_rate = max(100, current_rate - 25)
                self.tts_manager.set_rate(new_rate)
                return "Speaking slower now"
            
            elif "test voice" in command:
                await self.tts_manager.test_speech()
                return "Voice test completed"
            
            else:
                # Not a built-in command, let the conversation callback handle it
                return None
        
        except Exception as e:
            logger.error(f"Voice command processing failed: {e}")
            return "I'm sorry, I couldn't process that command."
    
    async def calibrate(self) -> bool:
        """Calibrate voice system"""
        try:
            logger.info("Starting voice system calibration...")
            
            success = True
            
            # Calibrate STT
            if self.stt_manager:
                await self.speak("Calibrating speech recognition. Please stay quiet for a moment.")
                stt_success = await self.stt_manager.calibrate()
                success = success and stt_success
            
            # Test TTS
            if self.tts_manager:
                await self.speak("Testing text to speech.")
                tts_success = await self.tts_manager.test_speech()
                success = success and tts_success
            
            # Test wake word detection
            if self.wake_word_detector:
                await self.speak(f"Testing wake word detection. Please say '{config.voice.wake_word}'.")
                wwd_success = self.wake_word_detector.test_detection()
                success = success and wwd_success
            
            if success:
                await self.speak("Voice system calibration completed successfully.")
                logger.info("Voice calibration successful")
            else:
                await self.speak("Voice system calibration encountered some issues.")
                logger.warning("Voice calibration had issues")
            
            return success
            
        except Exception as e:
            logger.error(f"Voice calibration failed: {e}")
            await self.speak("Voice calibration failed.")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get voice controller status"""
        status = {
            "enabled": self.enabled,
            "is_active": self.is_active,
            "is_listening": self.is_listening,
            "waiting_for_input": self.waiting_for_input
        }
        
        if self.stt_manager:
            status["stt"] = {
                "provider": self.stt_manager.provider,
                "language": self.stt_manager.language
            }
        
        if self.tts_manager:
            status["tts"] = self.tts_manager.get_status()
        
        if self.wake_word_detector:
            status["wake_word"] = self.wake_word_detector.get_status()
        
        return status
    
    async def set_voice_settings(self, settings: Dict[str, Any]) -> bool:
        """Update voice settings"""
        try:
            success = True
            
            if "volume" in settings and self.tts_manager:
                success = success and self.tts_manager.set_volume(settings["volume"])
            
            if "rate" in settings and self.tts_manager:
                success = success and self.tts_manager.set_rate(settings["rate"])
            
            if "voice" in settings and self.tts_manager:
                success = success and self.tts_manager.set_voice(settings["voice"])
            
            if "wake_word" in settings and self.wake_word_detector:
                self.wake_word_detector.set_wake_word(settings["wake_word"])
            
            if "sensitivity" in settings and self.wake_word_detector:
                self.wake_word_detector.set_sensitivity(settings["sensitivity"])
            
            if "stt_sensitivity" in settings and self.stt_manager:
                self.stt_manager.set_sensitivity(settings["stt_sensitivity"])
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to update voice settings: {e}")
            return False
    
    def get_available_voices(self) -> list:
        """Get list of available TTS voices"""
        if self.tts_manager:
            return self.tts_manager.get_available_voices()
        return []
    
    async def transcribe_file(self, file_path: str) -> Optional[str]:
        """Transcribe audio file to text"""
        if self.stt_manager:
            return await self.stt_manager.transcribe_file(file_path)
        return None
