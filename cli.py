"""
Jarvis AI Assistant - Command Line Interface
Main entry point for the Jarvis AI Assistant
"""

import asyncio
import sys
import signal
from pathlib import Path
from typing import Optional
import click
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt
from rich.table import Table

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from core.config import config
from core.logger import setup_logger, get_logger
from core.memory import memory_manager
from core.llm import llm_manager
from agents.agent_dispatcher import dispatcher
from voice.voice_controller import VoiceController
from skills.skill_manager import skill_manager
from jarvis_app import JarvisApp

console = Console()
logger = get_logger("cli")


class JarvisCLI:
    """Command Line Interface for Jarvis AI Assistant"""
    
    def __init__(self):
        self.app: Optional[JarvisApp] = None
        self.voice_controller: Optional[VoiceController] = None
        self.running = False
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        console.print("\n[yellow]Shutting down Jarvis...[/yellow]")
        self.running = False
        if self.app:
            asyncio.create_task(self.app.shutdown())
    
    async def initialize(self):
        """Initialize Jarvis components"""
        try:
            console.print("[blue]Initializing Jarvis AI Assistant...[/blue]")
            
            # Create directories
            config.create_directories()
            
            # Validate configuration
            config.validate_config()
            
            # Initialize main app
            self.app = JarvisApp()
            await self.app.initialize()
            
            # Initialize voice controller if enabled
            if config.voice.enabled:
                self.voice_controller = VoiceController()
            
            console.print("[green]✓ Jarvis initialized successfully[/green]")
            return True
            
        except Exception as e:
            console.print(f"[red]✗ Initialization failed: {e}[/red]")
            logger.error(f"Initialization failed: {e}")
            return False
    
    async def start_interactive_mode(self):
        """Start interactive chat mode"""
        console.print(Panel.fit(
            "[bold blue]Jarvis AI Assistant[/bold blue]\n"
            "Interactive Mode\n\n"
            "Type 'help' for commands, 'quit' to exit",
            title="🤖 Jarvis",
            border_style="blue"
        ))
        
        self.running = True
        
        while self.running:
            try:
                # Get user input
                user_input = Prompt.ask("\n[bold cyan]You[/bold cyan]")
                
                if not user_input.strip():
                    continue
                
                # Handle special commands
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    break
                elif user_input.lower() == 'help':
                    self._show_help()
                    continue
                elif user_input.lower() == 'status':
                    await self._show_status()
                    continue
                elif user_input.lower() == 'agents':
                    self._show_agents()
                    continue
                elif user_input.lower() == 'skills':
                    self._show_skills()
                    continue
                elif user_input.lower().startswith('voice'):
                    await self._handle_voice_command(user_input)
                    continue
                
                # Process with Jarvis
                console.print("[yellow]Jarvis is thinking...[/yellow]")
                
                response = await self.app.process_message(user_input)
                
                # Display response
                console.print(f"\n[bold green]Jarvis[/bold green]: {response}")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                console.print(f"[red]Error: {e}[/red]")
                logger.error(f"Interactive mode error: {e}")
        
        console.print("\n[yellow]Goodbye![/yellow]")
    
    async def start_voice_mode(self):
        """Start voice interaction mode"""
        if not self.voice_controller:
            console.print("[red]Voice control is not enabled[/red]")
            return
        
        console.print(Panel.fit(
            "[bold blue]Jarvis AI Assistant[/bold blue]\n"
            "Voice Mode\n\n"
            f"Say '{config.voice.wake_word}' to activate\n"
            "Press Ctrl+C to exit",
            title="🎤 Voice Control",
            border_style="green"
        ))
        
        try:
            # Start voice control
            success = await self.voice_controller.start(self.app.process_message)
            
            if not success:
                console.print("[red]Failed to start voice control[/red]")
                return
            
            self.running = True
            
            # Keep running until interrupted
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            console.print("\n[yellow]Stopping voice control...[/yellow]")
        finally:
            if self.voice_controller:
                await self.voice_controller.stop()
    
    async def start_server_mode(self, host: str = None, port: int = None):
        """Start web server mode"""
        try:
            from web.server import create_app
            import uvicorn
            
            host = host or config.app.host
            port = port or config.app.port
            
            console.print(f"[blue]Starting Jarvis web server on {host}:{port}[/blue]")
            
            # Create FastAPI app
            app = create_app(self.app)
            
            # Run server
            uvicorn.run(
                app,
                host=host,
                port=port,
                log_level="info"
            )
            
        except ImportError:
            console.print("[red]Web server dependencies not installed[/red]")
        except Exception as e:
            console.print(f"[red]Server failed to start: {e}[/red]")
            logger.error(f"Server startup failed: {e}")
    
    def _show_help(self):
        """Show help information"""
        help_text = """
[bold]Available Commands:[/bold]

[cyan]help[/cyan]     - Show this help message
[cyan]status[/cyan]   - Show system status
[cyan]agents[/cyan]   - List available agents
[cyan]skills[/cyan]   - List available skills
[cyan]voice[/cyan]    - Voice control commands
[cyan]quit[/cyan]     - Exit Jarvis

[bold]Voice Commands:[/bold]
[cyan]voice start[/cyan]  - Start voice control
[cyan]voice stop[/cyan]   - Stop voice control
[cyan]voice test[/cyan]   - Test voice system
[cyan]voice calibrate[/cyan] - Calibrate voice system

[bold]Examples:[/bold]
• "Create a Python script to calculate fibonacci numbers"
• "Search for information about artificial intelligence"
• "Open notepad"
• "What's the weather like today?"
• "Calculate 15 * 23 + 7"
        """
        console.print(Panel(help_text, title="Help", border_style="cyan"))
    
    async def _show_status(self):
        """Show system status"""
        try:
            # Get status from various components
            memory_stats = await memory_manager.get_memory_stats()
            agent_status = await dispatcher.get_agent_status()
            
            # Create status table
            table = Table(title="Jarvis System Status")
            table.add_column("Component", style="cyan")
            table.add_column("Status", style="green")
            table.add_column("Details")
            
            # LLM status
            providers = llm_manager.list_providers()
            table.add_row("LLM", "✓ Active", f"Providers: {', '.join(providers)}")
            
            # Memory status
            table.add_row(
                "Memory", 
                "✓ Active", 
                f"Total: {memory_stats.get('total_memories', 0)} memories"
            )
            
            # Agents status
            active_agents = sum(1 for agent in agent_status.values() 
                              if agent.get('status') != 'idle')
            table.add_row(
                "Agents", 
                "✓ Active", 
                f"Available: {len(agent_status)}, Active: {active_agents}"
            )
            
            # Skills status
            skills = skill_manager.list_skills()
            enabled_skills = sum(1 for skill in skills if skill.get('enabled', False))
            table.add_row(
                "Skills", 
                "✓ Active", 
                f"Available: {len(skills)}, Enabled: {enabled_skills}"
            )
            
            # Voice status
            if self.voice_controller:
                voice_status = self.voice_controller.get_status()
                status_text = "✓ Enabled" if voice_status.get('enabled') else "✗ Disabled"
                table.add_row("Voice", status_text, f"Active: {voice_status.get('is_active', False)}")
            else:
                table.add_row("Voice", "✗ Disabled", "Not initialized")
            
            console.print(table)
            
        except Exception as e:
            console.print(f"[red]Failed to get status: {e}[/red]")
    
    def _show_agents(self):
        """Show available agents"""
        agents = dispatcher.list_agents()
        
        table = Table(title="Available Agents")
        table.add_column("Name", style="cyan")
        table.add_column("Description")
        table.add_column("Status", style="green")
        table.add_column("Capabilities")
        
        for agent in agents:
            capabilities = ", ".join(agent.get('capabilities', [])[:3])
            if len(agent.get('capabilities', [])) > 3:
                capabilities += "..."
            
            table.add_row(
                agent['name'],
                agent['description'][:50] + "..." if len(agent['description']) > 50 else agent['description'],
                agent['status'],
                capabilities
            )
        
        console.print(table)
    
    def _show_skills(self):
        """Show available skills"""
        skills = skill_manager.list_skills()
        
        table = Table(title="Available Skills")
        table.add_column("Name", style="cyan")
        table.add_column("Description")
        table.add_column("Status", style="green")
        table.add_column("Keywords")
        
        for skill in skills:
            status = "✓ Enabled" if skill.get('enabled', False) else "✗ Disabled"
            keywords = ", ".join(skill.get('keywords', [])[:3])
            if len(skill.get('keywords', [])) > 3:
                keywords += "..."
            
            table.add_row(
                skill['name'],
                skill['description'][:40] + "..." if len(skill['description']) > 40 else skill['description'],
                status,
                keywords
            )
        
        console.print(table)
    
    async def _handle_voice_command(self, command: str):
        """Handle voice-related commands"""
        parts = command.lower().split()
        
        if len(parts) < 2:
            console.print("[red]Usage: voice [start|stop|test|calibrate][/red]")
            return
        
        action = parts[1]
        
        if not self.voice_controller:
            console.print("[red]Voice control is not available[/red]")
            return
        
        try:
            if action == "start":
                console.print("[blue]Starting voice control...[/blue]")
                success = await self.voice_controller.start(self.app.process_message)
                if success:
                    console.print("[green]✓ Voice control started[/green]")
                else:
                    console.print("[red]✗ Failed to start voice control[/red]")
            
            elif action == "stop":
                console.print("[blue]Stopping voice control...[/blue]")
                await self.voice_controller.stop()
                console.print("[green]✓ Voice control stopped[/green]")
            
            elif action == "test":
                console.print("[blue]Testing voice system...[/blue]")
                # Test TTS
                await self.voice_controller.speak("Voice system test successful")
                console.print("[green]✓ Voice test completed[/green]")
            
            elif action == "calibrate":
                console.print("[blue]Calibrating voice system...[/blue]")
                success = await self.voice_controller.calibrate()
                if success:
                    console.print("[green]✓ Voice calibration completed[/green]")
                else:
                    console.print("[red]✗ Voice calibration failed[/red]")
            
            else:
                console.print(f"[red]Unknown voice command: {action}[/red]")
        
        except Exception as e:
            console.print(f"[red]Voice command failed: {e}[/red]")
    
    async def shutdown(self):
        """Shutdown Jarvis"""
        try:
            console.print("[yellow]Shutting down Jarvis...[/yellow]")
            
            if self.voice_controller:
                await self.voice_controller.stop()
            
            if self.app:
                await self.app.shutdown()
            
            console.print("[green]✓ Jarvis shutdown complete[/green]")
            
        except Exception as e:
            console.print(f"[red]Shutdown error: {e}[/red]")
            logger.error(f"Shutdown error: {e}")


# CLI Commands
@click.group()
@click.option('--debug', is_flag=True, help='Enable debug mode')
@click.option('--config', help='Configuration file path')
def cli(debug, config_file):
    """Jarvis AI Assistant - Your personal AI companion"""
    if debug:
        config.app.debug = True
    
    if config_file:
        config.config_path = config_file
        config.load_config()
    
    # Setup logging
    setup_logger(
        level="DEBUG" if debug else config.logging.level,
        log_file=config.logging.file
    )


@cli.command()
@click.option('--voice', is_flag=True, help='Start in voice mode')
def start(voice):
    """Start Jarvis in interactive mode"""
    async def run():
        jarvis_cli = JarvisCLI()
        
        if not await jarvis_cli.initialize():
            return
        
        try:
            if voice:
                await jarvis_cli.start_voice_mode()
            else:
                await jarvis_cli.start_interactive_mode()
        finally:
            await jarvis_cli.shutdown()
    
    asyncio.run(run())


@cli.command()
@click.option('--host', default=None, help='Server host')
@click.option('--port', default=None, type=int, help='Server port')
def server(host, port):
    """Start Jarvis web server"""
    async def run():
        jarvis_cli = JarvisCLI()
        
        if not await jarvis_cli.initialize():
            return
        
        try:
            await jarvis_cli.start_server_mode(host, port)
        finally:
            await jarvis_cli.shutdown()
    
    asyncio.run(run())


@cli.command()
def test():
    """Test Jarvis components"""
    async def run():
        jarvis_cli = JarvisCLI()
        
        if not await jarvis_cli.initialize():
            return
        
        console.print("[blue]Testing Jarvis components...[/blue]")
        
        # Test LLM
        try:
            response = await llm_manager.generate("Say hello")
            console.print(f"[green]✓ LLM test: {response[:50]}...[/green]")
        except Exception as e:
            console.print(f"[red]✗ LLM test failed: {e}[/red]")
        
        # Test memory
        try:
            await memory_manager.store_memory("Test memory", memory_type="test")
            console.print("[green]✓ Memory test passed[/green]")
        except Exception as e:
            console.print(f"[red]✗ Memory test failed: {e}[/red]")
        
        # Test agents
        try:
            result = await dispatcher.dispatch_task("test task")
            console.print(f"[green]✓ Agent test: {result.message}[/green]")
        except Exception as e:
            console.print(f"[red]✗ Agent test failed: {e}[/red]")
        
        # Test voice (if enabled)
        if jarvis_cli.voice_controller:
            try:
                await jarvis_cli.voice_controller.speak("Voice test")
                console.print("[green]✓ Voice test passed[/green]")
            except Exception as e:
                console.print(f"[red]✗ Voice test failed: {e}[/red]")
        
        await jarvis_cli.shutdown()
    
    asyncio.run(run())


@cli.command()
def version():
    """Show Jarvis version information"""
    console.print(Panel.fit(
        f"[bold blue]Jarvis AI Assistant[/bold blue]\n"
        f"Version: {config.app.version}\n"
        f"Python: {sys.version.split()[0]}\n"
        f"Platform: {sys.platform}",
        title="Version Info",
        border_style="blue"
    ))


if __name__ == "__main__":
    cli()
