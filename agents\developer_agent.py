"""
Developer Agent for Jarvis AI Assistant
Handles code writing, debugging, and development tasks
"""

import os
import re
import ast
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from .base_agent import BaseAgent, AgentResult
from core.llm import llm_manager


class DeveloperAgent(BaseAgent):
    """Agent responsible for software development tasks"""
    
    def __init__(self):
        super().__init__(
            name="developer",
            description="Writes, debugs, and maintains code in various programming languages"
        )
        self.capabilities = [
            "code_writing",
            "code_debugging", 
            "code_review",
            "testing",
            "refactoring",
            "documentation",
            "dependency_management"
        ]
        self.supported_languages = [
            "python", "javascript", "typescript", "java", "c++", "c#",
            "go", "rust", "php", "ruby", "shell", "sql", "html", "css"
        ]
    
    def can_handle(self, task: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if this is a development task"""
        dev_keywords = [
            "code", "program", "script", "function", "class", "debug",
            "fix", "implement", "develop", "write", "create", "build",
            "test", "refactor", "optimize", "api", "algorithm"
        ]
        
        task_lower = task.lower()
        return any(keyword in task_lower for keyword in dev_keywords)
    
    async def execute(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> AgentResult:
        """Execute development task"""
        try:
            self.logger.info(f"Executing development task: {task}")
            
            # Determine the type of development task
            task_type = await self._classify_task(task, context)
            
            if task_type == "code_writing":
                result = await self._write_code(task, context)
            elif task_type == "debugging":
                result = await self._debug_code(task, context)
            elif task_type == "code_review":
                result = await self._review_code(task, context)
            elif task_type == "testing":
                result = await self._write_tests(task, context)
            elif task_type == "refactoring":
                result = await self._refactor_code(task, context)
            else:
                result = await self._general_development(task, context)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Development task failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="Development task execution failed"
            )
    
    async def _classify_task(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Classify the type of development task"""
        classification_prompt = f"""
        Classify this development task into one of these categories:
        
        Task: {task}
        Context: {context or {}}
        
        Categories:
        - code_writing: Writing new code, functions, classes, or scripts
        - debugging: Finding and fixing bugs or errors
        - code_review: Reviewing existing code for quality, security, or best practices
        - testing: Writing unit tests, integration tests, or test cases
        - refactoring: Improving existing code structure without changing functionality
        - general: Other development-related tasks
        
        Respond with just the category name.
        """
        
        try:
            response = await llm_manager.generate(
                prompt=classification_prompt,
                temperature=0.1
            )
            
            category = response.strip().lower()
            valid_categories = [
                "code_writing", "debugging", "code_review", 
                "testing", "refactoring", "general"
            ]
            
            return category if category in valid_categories else "general"
            
        except Exception as e:
            self.logger.warning(f"Task classification failed: {e}")
            return "general"
    
    async def _write_code(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Write new code based on requirements"""
        # Determine programming language
        language = await self._detect_language(task, context)
        
        code_prompt = f"""
        You are an expert {language} developer. Write high-quality code for this task:
        
        Task: {task}
        Context: {context or {}}
        Language: {language}
        
        Requirements:
        1. Write clean, readable, and well-documented code
        2. Follow best practices and conventions for {language}
        3. Include error handling where appropriate
        4. Add comments explaining complex logic
        5. Make the code modular and reusable
        
        Provide your response in this format:
        ```{language}
        # Your code here
        ```
        
        Also provide a brief explanation of what the code does and how to use it.
        """
        
        try:
            response = await llm_manager.generate(
                prompt=code_prompt,
                temperature=0.3
            )
            
            # Extract code from response
            code = self._extract_code_from_response(response, language)
            explanation = self._extract_explanation_from_response(response)
            
            # Validate the code if possible
            validation_result = await self._validate_code(code, language)
            
            return AgentResult(
                success=True,
                data={
                    "code": code,
                    "language": language,
                    "explanation": explanation,
                    "validation": validation_result
                },
                message=f"Generated {language} code successfully"
            )
            
        except Exception as e:
            self.logger.error(f"Code writing failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to write code"
            )
    
    async def _debug_code(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Debug existing code"""
        code = context.get("code", "") if context else ""
        error_message = context.get("error", "") if context else ""
        
        debug_prompt = f"""
        You are an expert debugger. Help fix this code issue:
        
        Task: {task}
        
        Code:
        ```
        {code}
        ```
        
        Error/Issue: {error_message}
        
        Please:
        1. Identify the problem
        2. Explain what's causing the issue
        3. Provide the corrected code
        4. Suggest how to prevent similar issues
        
        Format your response with:
        - Problem: [description]
        - Solution: [explanation]
        - Fixed Code: [corrected code in code blocks]
        - Prevention: [suggestions]
        """
        
        try:
            response = await llm_manager.generate(
                prompt=debug_prompt,
                temperature=0.2
            )
            
            # Parse the debugging response
            debug_info = self._parse_debug_response(response)
            
            return AgentResult(
                success=True,
                data=debug_info,
                message="Code debugging completed"
            )
            
        except Exception as e:
            self.logger.error(f"Code debugging failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to debug code"
            )
    
    async def _review_code(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Review code for quality and best practices"""
        code = context.get("code", "") if context else ""
        
        review_prompt = f"""
        You are a senior code reviewer. Review this code for:
        
        Code:
        ```
        {code}
        ```
        
        Review criteria:
        1. Code quality and readability
        2. Performance considerations
        3. Security issues
        4. Best practices adherence
        5. Potential bugs
        6. Documentation quality
        
        Provide:
        - Overall rating (1-10)
        - Strengths
        - Issues found
        - Recommendations for improvement
        - Specific suggestions with code examples if needed
        """
        
        try:
            response = await llm_manager.generate(
                prompt=review_prompt,
                temperature=0.3
            )
            
            review_info = self._parse_review_response(response)
            
            return AgentResult(
                success=True,
                data=review_info,
                message="Code review completed"
            )
            
        except Exception as e:
            self.logger.error(f"Code review failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to review code"
            )
    
    async def _write_tests(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Write tests for code"""
        code = context.get("code", "") if context else ""
        language = await self._detect_language(task, context)
        
        test_prompt = f"""
        Write comprehensive tests for this {language} code:
        
        Code to test:
        ```{language}
        {code}
        ```
        
        Create:
        1. Unit tests covering all functions/methods
        2. Edge cases and error conditions
        3. Integration tests if applicable
        4. Test data setup and teardown
        
        Use appropriate testing framework for {language}.
        Include comments explaining what each test verifies.
        """
        
        try:
            response = await llm_manager.generate(
                prompt=test_prompt,
                temperature=0.3
            )
            
            test_code = self._extract_code_from_response(response, language)
            
            return AgentResult(
                success=True,
                data={
                    "test_code": test_code,
                    "language": language,
                    "framework": self._get_test_framework(language)
                },
                message="Test code generated successfully"
            )
            
        except Exception as e:
            self.logger.error(f"Test writing failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to write tests"
            )
    
    async def _refactor_code(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Refactor existing code"""
        code = context.get("code", "") if context else ""
        
        refactor_prompt = f"""
        Refactor this code to improve:
        - Readability and maintainability
        - Performance
        - Code organization
        - Following best practices
        
        Original code:
        ```
        {code}
        ```
        
        Provide:
        1. Refactored code
        2. Explanation of changes made
        3. Benefits of the refactoring
        4. Any breaking changes to be aware of
        """
        
        try:
            response = await llm_manager.generate(
                prompt=refactor_prompt,
                temperature=0.3
            )
            
            refactor_info = self._parse_refactor_response(response)
            
            return AgentResult(
                success=True,
                data=refactor_info,
                message="Code refactoring completed"
            )
            
        except Exception as e:
            self.logger.error(f"Code refactoring failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to refactor code"
            )
    
    async def _general_development(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Handle general development tasks"""
        general_prompt = f"""
        You are an expert software developer. Help with this development task:
        
        Task: {task}
        Context: {context or {}}
        
        Provide a comprehensive solution including:
        1. Analysis of the requirements
        2. Recommended approach
        3. Code implementation (if applicable)
        4. Testing strategy
        5. Deployment considerations
        """
        
        try:
            response = await llm_manager.generate(
                prompt=general_prompt,
                temperature=0.4
            )
            
            return AgentResult(
                success=True,
                data={"solution": response},
                message="Development task completed"
            )
            
        except Exception as e:
            self.logger.error(f"General development task failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to complete development task"
            )
    
    async def _detect_language(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Detect programming language from task or context"""
        # Check context first
        if context and "language" in context:
            return context["language"].lower()
        
        # Check for language keywords in task
        task_lower = task.lower()
        
        language_keywords = {
            "python": ["python", "py", "django", "flask", "pandas", "numpy"],
            "javascript": ["javascript", "js", "node", "react", "vue", "angular"],
            "typescript": ["typescript", "ts"],
            "java": ["java", "spring", "maven", "gradle"],
            "c++": ["c++", "cpp", "cmake"],
            "c#": ["c#", "csharp", ".net", "dotnet"],
            "go": ["go", "golang"],
            "rust": ["rust", "cargo"],
            "php": ["php", "laravel", "symfony"],
            "ruby": ["ruby", "rails"],
            "shell": ["bash", "shell", "script", "sh"],
            "sql": ["sql", "database", "query"],
            "html": ["html", "web", "markup"],
            "css": ["css", "style", "stylesheet"]
        }
        
        for language, keywords in language_keywords.items():
            if any(keyword in task_lower for keyword in keywords):
                return language
        
        # Default to Python
        return "python"
    
    def _extract_code_from_response(self, response: str, language: str) -> str:
        """Extract code blocks from LLM response"""
        # Look for code blocks with language specification
        pattern = rf"```{language}\s*\n(.*?)\n```"
        matches = re.findall(pattern, response, re.DOTALL | re.IGNORECASE)
        
        if matches:
            return matches[0].strip()
        
        # Look for generic code blocks
        pattern = r"```\s*\n(.*?)\n```"
        matches = re.findall(pattern, response, re.DOTALL)
        
        if matches:
            return matches[0].strip()
        
        # Return the whole response if no code blocks found
        return response.strip()
    
    def _extract_explanation_from_response(self, response: str) -> str:
        """Extract explanation text from LLM response"""
        # Remove code blocks
        cleaned = re.sub(r"```.*?```", "", response, flags=re.DOTALL)
        return cleaned.strip()
    
    async def _validate_code(self, code: str, language: str) -> Dict[str, Any]:
        """Validate code syntax and basic structure"""
        validation_result = {
            "syntax_valid": False,
            "errors": [],
            "warnings": []
        }
        
        try:
            if language == "python":
                # Parse Python code
                ast.parse(code)
                validation_result["syntax_valid"] = True
            else:
                # For other languages, we'll skip syntax validation for now
                validation_result["syntax_valid"] = True
                validation_result["warnings"].append(
                    f"Syntax validation not implemented for {language}"
                )
                
        except SyntaxError as e:
            validation_result["errors"].append(f"Syntax error: {e}")
        except Exception as e:
            validation_result["errors"].append(f"Validation error: {e}")
        
        return validation_result
    
    def _parse_debug_response(self, response: str) -> Dict[str, Any]:
        """Parse debugging response into structured data"""
        debug_info = {
            "problem": "",
            "solution": "",
            "fixed_code": "",
            "prevention": ""
        }
        
        # Simple parsing - look for sections
        sections = response.split("\n")
        current_section = None
        
        for line in sections:
            line = line.strip()
            if line.lower().startswith("problem:"):
                current_section = "problem"
                debug_info["problem"] = line[8:].strip()
            elif line.lower().startswith("solution:"):
                current_section = "solution"
                debug_info["solution"] = line[9:].strip()
            elif line.lower().startswith("prevention:"):
                current_section = "prevention"
                debug_info["prevention"] = line[11:].strip()
            elif "```" in line:
                # Extract code blocks
                code_match = re.search(r"```.*?\n(.*?)\n```", response, re.DOTALL)
                if code_match:
                    debug_info["fixed_code"] = code_match.group(1).strip()
            elif current_section and line:
                debug_info[current_section] += " " + line
        
        return debug_info
    
    def _parse_review_response(self, response: str) -> Dict[str, Any]:
        """Parse code review response"""
        return {
            "review": response,
            "rating": self._extract_rating(response),
            "issues": self._extract_issues(response),
            "recommendations": self._extract_recommendations(response)
        }
    
    def _parse_refactor_response(self, response: str) -> Dict[str, Any]:
        """Parse refactoring response"""
        return {
            "refactored_code": self._extract_code_from_response(response, ""),
            "explanation": self._extract_explanation_from_response(response),
            "changes": [],
            "benefits": []
        }
    
    def _extract_rating(self, text: str) -> Optional[int]:
        """Extract rating from review text"""
        rating_match = re.search(r"rating.*?(\d+)", text, re.IGNORECASE)
        return int(rating_match.group(1)) if rating_match else None
    
    def _extract_issues(self, text: str) -> List[str]:
        """Extract issues from review text"""
        # Simple extraction - look for bullet points or numbered lists
        issues = []
        lines = text.split("\n")
        
        for line in lines:
            line = line.strip()
            if (line.startswith("-") or line.startswith("*") or 
                re.match(r"^\d+\.", line)):
                issues.append(line)
        
        return issues
    
    def _extract_recommendations(self, text: str) -> List[str]:
        """Extract recommendations from review text"""
        # Similar to issues extraction
        return self._extract_issues(text)
    
    def _get_test_framework(self, language: str) -> str:
        """Get appropriate test framework for language"""
        frameworks = {
            "python": "pytest",
            "javascript": "jest",
            "typescript": "jest",
            "java": "junit",
            "c++": "gtest",
            "c#": "nunit",
            "go": "testing",
            "rust": "cargo test",
            "php": "phpunit",
            "ruby": "rspec"
        }
        
        return frameworks.get(language, "standard")
