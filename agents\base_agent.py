"""
Base agent class for Jarvis AI Assistant
"""

import asyncio
import json
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

from core.llm import llm_manager
from core.memory import memory_manager
from core.logger import get_logger

logger = get_logger("agents")


class AgentStatus(Enum):
    """Agent execution status"""
    IDLE = "idle"
    THINKING = "thinking"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class AgentResult:
    """Result of agent execution"""
    success: bool
    data: Any = None
    message: str = ""
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "success": self.success,
            "data": self.data,
            "message": self.message,
            "error": self.error,
            "metadata": self.metadata or {}
        }


class BaseAgent(ABC):
    """Base class for all agents"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.status = AgentStatus.IDLE
        self.current_task = None
        self.execution_history: List[Dict[str, Any]] = []
        self.capabilities: List[str] = []
        self.logger = get_logger(f"agents.{name}")
    
    @abstractmethod
    async def execute(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> AgentResult:
        """Execute a task"""
        pass
    
    @abstractmethod
    def can_handle(self, task: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if agent can handle the given task"""
        pass
    
    async def think(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Think about how to approach the task"""
        self.status = AgentStatus.THINKING
        
        # Get relevant memories
        memories = await memory_manager.search_memories(
            query=task,
            memory_type="task",
            limit=5
        )
        
        memory_context = ""
        if memories:
            memory_context = "\n".join([
                f"- {memory[0].content}" for memory in memories
            ])
        
        # Create thinking prompt
        thinking_prompt = f"""
        You are the {self.name} agent. Your role is: {self.description}
        
        Task: {task}
        
        Context: {json.dumps(context or {}, indent=2)}
        
        Relevant past experiences:
        {memory_context}
        
        Your capabilities: {', '.join(self.capabilities)}
        
        Think step by step about how to approach this task:
        1. What is the main objective?
        2. What steps are needed?
        3. What tools or methods will you use?
        4. What potential challenges might arise?
        5. How will you measure success?
        
        Respond with a JSON object containing:
        {{
            "approach": "your approach description",
            "steps": ["step 1", "step 2", ...],
            "tools_needed": ["tool1", "tool2", ...],
            "challenges": ["challenge1", "challenge2", ...],
            "success_criteria": "how to measure success"
        }}
        """
        
        try:
            response = await llm_manager.generate(
                prompt=thinking_prompt,
                temperature=0.3
            )
            
            # Try to parse JSON response
            thinking_result = json.loads(response)
            
            self.logger.info(f"Thinking completed for task: {task}")
            return thinking_result
            
        except json.JSONDecodeError:
            self.logger.warning("Failed to parse thinking response as JSON")
            return {
                "approach": response,
                "steps": [],
                "tools_needed": [],
                "challenges": [],
                "success_criteria": "Task completion"
            }
        except Exception as e:
            self.logger.error(f"Thinking failed: {e}")
            return {
                "approach": "Direct execution",
                "steps": ["Execute task"],
                "tools_needed": [],
                "challenges": [str(e)],
                "success_criteria": "No errors"
            }
    
    async def reflect(
        self,
        task: str,
        result: AgentResult,
        thinking: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Reflect on the execution result"""
        reflection_prompt = f"""
        You are the {self.name} agent reflecting on a completed task.
        
        Original task: {task}
        Your approach: {thinking.get('approach', 'Unknown')}
        Planned steps: {thinking.get('steps', [])}
        
        Execution result:
        - Success: {result.success}
        - Message: {result.message}
        - Error: {result.error}
        
        Reflect on this execution:
        1. What went well?
        2. What could be improved?
        3. What did you learn?
        4. How would you approach similar tasks in the future?
        
        Respond with a JSON object containing:
        {{
            "went_well": ["thing1", "thing2", ...],
            "improvements": ["improvement1", "improvement2", ...],
            "learnings": ["learning1", "learning2", ...],
            "future_approach": "description of future approach"
        }}
        """
        
        try:
            response = await llm_manager.generate(
                prompt=reflection_prompt,
                temperature=0.3
            )
            
            reflection_result = json.loads(response)
            
            # Store reflection as memory
            await memory_manager.store_memory(
                content=f"Task: {task}\nReflection: {json.dumps(reflection_result)}",
                metadata={
                    "agent": self.name,
                    "task": task,
                    "success": result.success
                },
                memory_type="reflection",
                importance=0.7 if result.success else 0.9
            )
            
            return reflection_result
            
        except Exception as e:
            self.logger.error(f"Reflection failed: {e}")
            return {
                "went_well": [],
                "improvements": [],
                "learnings": [],
                "future_approach": "Continue with current approach"
            }
    
    async def execute_with_thinking(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> AgentResult:
        """Execute task with thinking and reflection"""
        start_time = datetime.now()
        self.current_task = task
        
        try:
            # Think about the task
            thinking = await self.think(task, context)
            
            # Execute the task
            self.status = AgentStatus.EXECUTING
            result = await self.execute(task, context, **kwargs)
            
            # Reflect on the result
            reflection = await self.reflect(task, result, thinking)
            
            # Update execution history
            execution_record = {
                "task": task,
                "context": context,
                "thinking": thinking,
                "result": result.to_dict(),
                "reflection": reflection,
                "start_time": start_time.isoformat(),
                "end_time": datetime.now().isoformat(),
                "duration": (datetime.now() - start_time).total_seconds()
            }
            
            self.execution_history.append(execution_record)
            
            # Limit history size
            if len(self.execution_history) > 100:
                self.execution_history = self.execution_history[-50:]
            
            self.status = AgentStatus.COMPLETED if result.success else AgentStatus.FAILED
            self.current_task = None
            
            return result
            
        except Exception as e:
            self.logger.error(f"Execution failed: {e}")
            self.status = AgentStatus.FAILED
            self.current_task = None
            
            return AgentResult(
                success=False,
                error=str(e),
                message=f"Agent {self.name} execution failed"
            )
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "name": self.name,
            "description": self.description,
            "status": self.status.value,
            "current_task": self.current_task,
            "capabilities": self.capabilities,
            "execution_count": len(self.execution_history)
        }
    
    def get_execution_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent execution history"""
        return self.execution_history[-limit:]
