"""
Speech-to-Text Manager for Jarvis AI Assistant
"""

import asyncio
import io
import wave
import threading
from typing import Optional, Callable, Any
import speech_recognition as sr
import pyaudio
import whisper
from core.config import config
from core.logger import get_logger

logger = get_logger("stt")


class STTManager:
    """Manages speech-to-text functionality"""
    
    def __init__(self):
        self.provider = config.voice.stt_provider
        self.language = config.voice.language
        self.recognizer = sr.Recognizer()
        self.microphone = None
        self.whisper_model = None
        self.is_listening = False
        self.audio_queue = asyncio.Queue()
        self.callback_function: Optional[Callable] = None
        
        # Audio settings
        self.sample_rate = 16000
        self.chunk_size = 1024
        self.channels = 1
        self.format = pyaudio.paInt16
        
        self.initialize()
    
    def initialize(self):
        """Initialize STT system"""
        try:
            # Initialize microphone
            self.microphone = sr.Microphone()
            
            # Adjust for ambient noise
            with self.microphone as source:
                logger.info("Adjusting for ambient noise...")
                self.recognizer.adjust_for_ambient_noise(source, duration=2)
            
            # Initialize provider-specific components
            if self.provider == "whisper":
                self._initialize_whisper()
            elif self.provider == "google":
                self._initialize_google()
            
            logger.info(f"STT initialized with provider: {self.provider}")
            
        except Exception as e:
            logger.error(f"STT initialization failed: {e}")
            raise
    
    def _initialize_whisper(self):
        """Initialize Whisper model"""
        try:
            logger.info("Loading Whisper model...")
            self.whisper_model = whisper.load_model("base")
            logger.info("Whisper model loaded successfully")
        except Exception as e:
            logger.error(f"Whisper initialization failed: {e}")
            raise
    
    def _initialize_google(self):
        """Initialize Google Speech Recognition"""
        # Google STT uses the speech_recognition library
        # No additional initialization needed
        logger.info("Google STT initialized")
    
    async def listen_once(self, timeout: float = 5.0) -> Optional[str]:
        """Listen for a single utterance"""
        try:
            logger.info("Listening for speech...")
            
            with self.microphone as source:
                # Listen for audio
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=10)
            
            # Recognize speech
            text = await self._recognize_audio(audio)
            
            if text:
                logger.info(f"Recognized: {text}")
                return text.strip()
            else:
                logger.warning("No speech recognized")
                return None
                
        except sr.WaitTimeoutError:
            logger.warning("Listening timeout")
            return None
        except Exception as e:
            logger.error(f"Speech recognition failed: {e}")
            return None
    
    async def start_continuous_listening(self, callback: Callable[[str], None]):
        """Start continuous listening mode"""
        if self.is_listening:
            logger.warning("Already listening")
            return
        
        self.callback_function = callback
        self.is_listening = True
        
        logger.info("Starting continuous listening...")
        
        # Start listening in a separate thread
        listen_thread = threading.Thread(target=self._continuous_listen_thread)
        listen_thread.daemon = True
        listen_thread.start()
    
    def stop_continuous_listening(self):
        """Stop continuous listening mode"""
        if not self.is_listening:
            return
        
        self.is_listening = False
        self.callback_function = None
        logger.info("Stopped continuous listening")
    
    def _continuous_listen_thread(self):
        """Continuous listening thread"""
        while self.is_listening:
            try:
                with self.microphone as source:
                    # Listen for audio with shorter timeout for responsiveness
                    audio = self.recognizer.listen(source, timeout=1, phrase_time_limit=5)
                
                # Process audio in background
                asyncio.create_task(self._process_continuous_audio(audio))
                
            except sr.WaitTimeoutError:
                # Timeout is expected in continuous mode
                continue
            except Exception as e:
                logger.error(f"Continuous listening error: {e}")
                break
    
    async def _process_continuous_audio(self, audio):
        """Process audio from continuous listening"""
        try:
            text = await self._recognize_audio(audio)
            
            if text and self.callback_function:
                logger.info(f"Continuous recognition: {text}")
                self.callback_function(text.strip())
                
        except Exception as e:
            logger.error(f"Continuous audio processing failed: {e}")
    
    async def _recognize_audio(self, audio) -> Optional[str]:
        """Recognize speech from audio data"""
        try:
            if self.provider == "whisper":
                return await self._recognize_with_whisper(audio)
            elif self.provider == "google":
                return await self._recognize_with_google(audio)
            else:
                logger.error(f"Unknown STT provider: {self.provider}")
                return None
                
        except Exception as e:
            logger.error(f"Audio recognition failed: {e}")
            return None
    
    async def _recognize_with_whisper(self, audio) -> Optional[str]:
        """Recognize speech using Whisper"""
        try:
            # Convert audio to format Whisper expects
            audio_data = io.BytesIO(audio.get_wav_data())
            
            # Save to temporary file
            import tempfile
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_file.write(audio_data.getvalue())
                temp_file_path = temp_file.name
            
            # Transcribe with Whisper
            result = self.whisper_model.transcribe(temp_file_path)
            
            # Clean up temporary file
            import os
            os.unlink(temp_file_path)
            
            return result["text"]
            
        except Exception as e:
            logger.error(f"Whisper recognition failed: {e}")
            return None
    
    async def _recognize_with_google(self, audio) -> Optional[str]:
        """Recognize speech using Google Speech Recognition"""
        try:
            # Use Google's free speech recognition
            text = self.recognizer.recognize_google(audio, language=self.language)
            return text
            
        except sr.UnknownValueError:
            logger.debug("Google STT could not understand audio")
            return None
        except sr.RequestError as e:
            logger.error(f"Google STT request failed: {e}")
            return None
    
    async def transcribe_file(self, file_path: str) -> Optional[str]:
        """Transcribe audio from file"""
        try:
            logger.info(f"Transcribing file: {file_path}")
            
            if self.provider == "whisper":
                result = self.whisper_model.transcribe(file_path)
                return result["text"]
            else:
                # Use speech_recognition for other providers
                with sr.AudioFile(file_path) as source:
                    audio = self.recognizer.record(source)
                
                return await self._recognize_audio(audio)
                
        except Exception as e:
            logger.error(f"File transcription failed: {e}")
            return None
    
    def set_sensitivity(self, energy_threshold: int):
        """Set microphone sensitivity"""
        self.recognizer.energy_threshold = energy_threshold
        logger.info(f"Set microphone sensitivity to: {energy_threshold}")
    
    def set_pause_threshold(self, pause_threshold: float):
        """Set pause threshold for speech detection"""
        self.recognizer.pause_threshold = pause_threshold
        logger.info(f"Set pause threshold to: {pause_threshold}")
    
    def get_microphone_info(self) -> dict:
        """Get microphone information"""
        try:
            p = pyaudio.PyAudio()
            info = {
                "device_count": p.get_device_count(),
                "default_input_device": p.get_default_input_device_info(),
                "devices": []
            }
            
            for i in range(p.get_device_count()):
                device_info = p.get_device_info_by_index(i)
                if device_info["maxInputChannels"] > 0:
                    info["devices"].append({
                        "index": i,
                        "name": device_info["name"],
                        "channels": device_info["maxInputChannels"],
                        "sample_rate": device_info["defaultSampleRate"]
                    })
            
            p.terminate()
            return info
            
        except Exception as e:
            logger.error(f"Failed to get microphone info: {e}")
            return {}
    
    def test_microphone(self) -> bool:
        """Test microphone functionality"""
        try:
            logger.info("Testing microphone...")
            
            with self.microphone as source:
                logger.info("Say something...")
                audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=3)
            
            # Try to recognize the test audio
            try:
                text = self.recognizer.recognize_google(audio)
                logger.info(f"Microphone test successful. Heard: {text}")
                return True
            except:
                logger.info("Microphone test successful (audio captured but not recognized)")
                return True
                
        except Exception as e:
            logger.error(f"Microphone test failed: {e}")
            return False
    
    async def calibrate(self) -> bool:
        """Calibrate microphone for optimal performance"""
        try:
            logger.info("Calibrating microphone...")
            
            # Adjust for ambient noise multiple times for better calibration
            with self.microphone as source:
                logger.info("Measuring ambient noise (stay quiet)...")
                self.recognizer.adjust_for_ambient_noise(source, duration=3)
                
                initial_threshold = self.recognizer.energy_threshold
                logger.info(f"Initial energy threshold: {initial_threshold}")
                
                # Fine-tune with multiple samples
                for i in range(3):
                    logger.info(f"Calibration sample {i+1}/3...")
                    self.recognizer.adjust_for_ambient_noise(source, duration=1)
                
                final_threshold = self.recognizer.energy_threshold
                logger.info(f"Final energy threshold: {final_threshold}")
            
            # Test with speech
            logger.info("Calibration complete. Please say 'test' to verify...")
            test_result = await self.listen_once(timeout=10)
            
            if test_result and "test" in test_result.lower():
                logger.info("Calibration successful!")
                return True
            else:
                logger.warning("Calibration may need adjustment")
                return False
                
        except Exception as e:
            logger.error(f"Calibration failed: {e}")
            return False
