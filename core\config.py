"""
Configuration management for Jarvis AI Assistant
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv
from pydantic import BaseModel, Field


class AppConfig(BaseModel):
    name: str = "Jarvis AI Assistant"
    version: str = "1.0.0"
    debug: bool = True
    host: str = "0.0.0.0"
    port: int = 8000


class LLMConfig(BaseModel):
    provider: str = "groq"
    model: str = "mixtral-8x7b-32768"
    temperature: float = 0.7
    max_tokens: int = 4096
    timeout: int = 30


class VoiceConfig(BaseModel):
    enabled: bool = True
    wake_word: str = "jarvis"
    stt_provider: str = "whisper"
    tts_provider: str = "pyttsx3"
    language: str = "en-US"
    voice_rate: int = 200
    voice_volume: float = 0.9


class MemoryConfig(BaseModel):
    provider: str = "chromadb"
    persist_directory: str = "./data/memory"
    collection_name: str = "jarvis_memory"
    embedding_model: str = "all-MiniLM-L6-v2"
    max_memory_items: int = 10000


class AgentConfig(BaseModel):
    max_concurrent: int = 5
    timeout: int = 300
    retry_attempts: int = 3
    available: list = Field(default_factory=lambda: [
        "planner", "developer", "shell", "file_manager", 
        "browser", "system_control", "research"
    ])


class SkillsConfig(BaseModel):
    auto_load: bool = True
    skills_directory: str = "./skills"
    max_execution_time: int = 600


class SystemConfig(BaseModel):
    allow_system_commands: bool = True
    safe_mode: bool = False
    restricted_commands: list = Field(default_factory=lambda: [
        "rm -rf", "del /f /s /q", "format", "shutdown"
    ])


class DockerConfig(BaseModel):
    enabled: bool = True
    network: str = "jarvis_network"
    volumes: list = Field(default_factory=lambda: [
        "./data:/app/data",
        "./skills:/app/skills"
    ])


class LoggingConfig(BaseModel):
    level: str = "INFO"
    file: str = "./logs/jarvis.log"
    max_size: str = "10MB"
    backup_count: int = 5


class Config:
    """Main configuration class for Jarvis AI Assistant"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config.yaml"
        self.load_environment()
        self.load_config()
    
    def load_environment(self):
        """Load environment variables from .env file"""
        env_path = Path(".env")
        if env_path.exists():
            load_dotenv(env_path)
    
    def load_config(self):
        """Load configuration from YAML file"""
        config_file = Path(self.config_path)
        
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
        else:
            config_data = {}
        
        # Initialize configuration sections
        self.app = AppConfig(**config_data.get('app', {}))
        self.llm = LLMConfig(**config_data.get('llm', {}))
        self.voice = VoiceConfig(**config_data.get('voice', {}))
        self.memory = MemoryConfig(**config_data.get('memory', {}))
        self.agents = AgentConfig(**config_data.get('agents', {}))
        self.skills = SkillsConfig(**config_data.get('skills', {}))
        self.system = SystemConfig(**config_data.get('system', {}))
        self.docker = DockerConfig(**config_data.get('docker', {}))
        self.logging = LoggingConfig(**config_data.get('logging', {}))
        
        # API Keys from environment
        self.api_keys = {
            'groq_api_key': os.getenv('GROQ_API_KEY'),
            'openai_api_key': os.getenv('OPENAI_API_KEY'),
            'elevenlabs_api_key': os.getenv('ELEVENLABS_API_KEY'),
            'google_cloud_api_key': os.getenv('GOOGLE_CLOUD_API_KEY'),
            'azure_speech_key': os.getenv('AZURE_SPEECH_KEY'),
        }
    
    def get_api_key(self, service: str) -> Optional[str]:
        """Get API key for a specific service"""
        return self.api_keys.get(f'{service}_api_key')
    
    def validate_config(self) -> bool:
        """Validate configuration and required API keys"""
        required_keys = ['groq_api_key']
        
        for key in required_keys:
            if not self.api_keys.get(key):
                raise ValueError(f"Missing required API key: {key}")
        
        return True
    
    def create_directories(self):
        """Create necessary directories"""
        directories = [
            self.memory.persist_directory,
            Path(self.logging.file).parent,
            self.skills.skills_directory,
            "./data",
            "./data/audio"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)


# Global configuration instance
config = Config()
