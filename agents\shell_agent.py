"""
Shell Agent for Jarvis AI Assistant
Handles command line operations and system commands
"""

import os
import subprocess
import shlex
import platform
import async<PERSON>
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from .base_agent import BaseAgent, AgentResult
from core.config import config
from core.llm import llm_manager


class ShellAgent(BaseAgent):
    """Agent responsible for shell/command line operations"""
    
    def __init__(self):
        super().__init__(
            name="shell",
            description="Executes command line operations and system commands safely"
        )
        self.capabilities = [
            "command_execution",
            "script_running",
            "process_management",
            "environment_setup",
            "system_monitoring"
        ]
        self.current_directory = os.getcwd()
        self.environment_vars = os.environ.copy()
        self.command_history: List[Dict[str, Any]] = []
    
    def can_handle(self, task: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if this is a shell/command task"""
        shell_keywords = [
            "run", "execute", "command", "script", "terminal", "shell",
            "install", "update", "start", "stop", "kill", "process",
            "directory", "folder", "file", "list", "ls", "cd", "mkdir",
            "git", "npm", "pip", "docker", "curl", "wget"
        ]
        
        task_lower = task.lower()
        return any(keyword in task_lower for keyword in shell_keywords)
    
    async def execute(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> AgentResult:
        """Execute shell task"""
        try:
            self.logger.info(f"Executing shell task: {task}")
            
            # Determine if we need to generate commands or execute provided ones
            if context and "commands" in context:
                commands = context["commands"]
            else:
                commands = await self._generate_commands(task, context)
            
            # Validate commands for safety
            safe_commands = await self._validate_commands(commands)
            
            if not safe_commands:
                return AgentResult(
                    success=False,
                    message="No safe commands to execute",
                    error="All commands were filtered for safety"
                )
            
            # Execute commands
            results = []
            for command in safe_commands:
                result = await self._execute_command(command)
                results.append(result)
                
                # Stop on first failure if not specified otherwise
                if not result["success"] and not kwargs.get("continue_on_error", False):
                    break
            
            # Determine overall success
            success = all(r["success"] for r in results)
            
            return AgentResult(
                success=success,
                data={
                    "commands": safe_commands,
                    "results": results,
                    "working_directory": self.current_directory
                },
                message=f"Executed {len(results)} commands, {sum(1 for r in results if r['success'])} successful"
            )
            
        except Exception as e:
            self.logger.error(f"Shell task failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="Shell task execution failed"
            )
    
    async def _generate_commands(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """Generate shell commands for the task"""
        system_info = {
            "os": platform.system(),
            "platform": platform.platform(),
            "current_directory": self.current_directory,
            "shell": os.environ.get("SHELL", "cmd" if platform.system() == "Windows" else "bash")
        }
        
        command_prompt = f"""
        You are an expert system administrator. Generate shell commands for this task:
        
        Task: {task}
        Context: {context or {}}
        System: {system_info}
        
        Requirements:
        1. Generate safe, appropriate commands for the operating system
        2. Use relative paths when possible
        3. Include error checking where appropriate
        4. Avoid destructive operations unless explicitly requested
        5. Consider the current working directory
        
        Respond with a JSON array of commands:
        ["command1", "command2", "command3"]
        
        Each command should be a single line that can be executed independently.
        """
        
        try:
            response = await llm_manager.generate(
                prompt=command_prompt,
                temperature=0.2
            )
            
            # Try to parse as JSON
            import json
            commands = json.loads(response)
            
            if isinstance(commands, list):
                return [str(cmd) for cmd in commands]
            else:
                return [str(commands)]
                
        except json.JSONDecodeError:
            # Fallback: extract commands from text
            lines = response.strip().split('\n')
            commands = []
            
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('//'):
                    # Remove common prefixes
                    for prefix in ['$', '>', '>>']:
                        if line.startswith(prefix):
                            line = line[len(prefix):].strip()
                    commands.append(line)
            
            return commands
        except Exception as e:
            self.logger.error(f"Command generation failed: {e}")
            return []
    
    async def _validate_commands(self, commands: List[str]) -> List[str]:
        """Validate commands for safety"""
        if not config.system.allow_system_commands:
            self.logger.warning("System commands are disabled")
            return []
        
        safe_commands = []
        restricted = config.system.restricted_commands
        
        for command in commands:
            command = command.strip()
            if not command:
                continue
            
            # Check against restricted commands
            is_restricted = False
            for restricted_cmd in restricted:
                if restricted_cmd.lower() in command.lower():
                    self.logger.warning(f"Restricted command blocked: {command}")
                    is_restricted = True
                    break
            
            if not is_restricted:
                # Additional safety checks
                if self._is_safe_command(command):
                    safe_commands.append(command)
                else:
                    self.logger.warning(f"Unsafe command blocked: {command}")
        
        return safe_commands
    
    def _is_safe_command(self, command: str) -> bool:
        """Check if a command is safe to execute"""
        command_lower = command.lower()
        
        # Dangerous patterns
        dangerous_patterns = [
            "rm -rf /",
            "del /f /s /q c:",
            "format c:",
            "dd if=/dev/zero",
            ":(){ :|:& };:",  # Fork bomb
            "chmod 777 /",
            "chown -R",
            "sudo rm",
            "sudo dd"
        ]
        
        for pattern in dangerous_patterns:
            if pattern in command_lower:
                return False
        
        # In safe mode, only allow specific commands
        if config.system.safe_mode:
            safe_commands = [
                "ls", "dir", "pwd", "cd", "cat", "type", "echo", "grep",
                "find", "which", "whereis", "ps", "top", "df", "du",
                "git", "npm", "pip", "python", "node", "java", "curl", "wget"
            ]
            
            command_parts = shlex.split(command)
            if command_parts:
                base_command = command_parts[0].split('/')[-1]  # Get command name
                return base_command in safe_commands
        
        return True
    
    async def _execute_command(self, command: str) -> Dict[str, Any]:
        """Execute a single command"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            self.logger.info(f"Executing command: {command}")
            
            # Prepare command execution
            if platform.system() == "Windows":
                # Windows command execution
                process = await asyncio.create_subprocess_shell(
                    command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=self.current_directory,
                    env=self.environment_vars
                )
            else:
                # Unix-like command execution
                process = await asyncio.create_subprocess_shell(
                    command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=self.current_directory,
                    env=self.environment_vars
                )
            
            # Wait for completion with timeout
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=300  # 5 minute timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                raise TimeoutError("Command execution timed out")
            
            # Decode output
            stdout_text = stdout.decode('utf-8', errors='replace') if stdout else ""
            stderr_text = stderr.decode('utf-8', errors='replace') if stderr else ""
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            # Update working directory if cd command
            if command.strip().startswith('cd '):
                new_dir = command.strip()[3:].strip()
                if new_dir:
                    try:
                        if os.path.isabs(new_dir):
                            self.current_directory = new_dir
                        else:
                            self.current_directory = os.path.join(self.current_directory, new_dir)
                        self.current_directory = os.path.abspath(self.current_directory)
                    except Exception as e:
                        self.logger.warning(f"Failed to update directory: {e}")
            
            result = {
                "command": command,
                "success": process.returncode == 0,
                "return_code": process.returncode,
                "stdout": stdout_text,
                "stderr": stderr_text,
                "execution_time": execution_time,
                "working_directory": self.current_directory
            }
            
            # Add to command history
            self.command_history.append(result)
            
            # Limit history size
            if len(self.command_history) > 100:
                self.command_history = self.command_history[-50:]
            
            return result
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            
            result = {
                "command": command,
                "success": False,
                "return_code": -1,
                "stdout": "",
                "stderr": str(e),
                "execution_time": execution_time,
                "working_directory": self.current_directory
            }
            
            self.command_history.append(result)
            return result
    
    async def change_directory(self, path: str) -> AgentResult:
        """Change working directory"""
        try:
            if os.path.isabs(path):
                new_path = path
            else:
                new_path = os.path.join(self.current_directory, path)
            
            new_path = os.path.abspath(new_path)
            
            if os.path.exists(new_path) and os.path.isdir(new_path):
                old_directory = self.current_directory
                self.current_directory = new_path
                
                return AgentResult(
                    success=True,
                    data={
                        "old_directory": old_directory,
                        "new_directory": self.current_directory
                    },
                    message=f"Changed directory to {self.current_directory}"
                )
            else:
                return AgentResult(
                    success=False,
                    error="Directory does not exist",
                    message=f"Cannot change to directory: {new_path}"
                )
                
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to change directory"
            )
    
    async def set_environment_variable(self, name: str, value: str) -> AgentResult:
        """Set environment variable"""
        try:
            self.environment_vars[name] = value
            
            return AgentResult(
                success=True,
                data={"name": name, "value": value},
                message=f"Set environment variable {name}={value}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to set environment variable"
            )
    
    async def get_system_info(self) -> AgentResult:
        """Get system information"""
        try:
            info = {
                "platform": platform.platform(),
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "current_directory": self.current_directory,
                "environment_vars": dict(self.environment_vars),
                "python_version": platform.python_version()
            }
            
            return AgentResult(
                success=True,
                data=info,
                message="Retrieved system information"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to get system information"
            )
    
    def get_command_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent command history"""
        return self.command_history[-limit:]
    
    async def run_script(self, script_path: str, args: List[str] = None) -> AgentResult:
        """Run a script file"""
        try:
            if not os.path.exists(script_path):
                return AgentResult(
                    success=False,
                    error="Script file not found",
                    message=f"Script not found: {script_path}"
                )
            
            # Determine script type and execution method
            script_ext = Path(script_path).suffix.lower()
            
            if script_ext == ".py":
                command = f"python {script_path}"
            elif script_ext == ".sh":
                command = f"bash {script_path}"
            elif script_ext == ".bat":
                command = script_path
            elif script_ext == ".ps1":
                command = f"powershell -ExecutionPolicy Bypass -File {script_path}"
            else:
                command = script_path
            
            # Add arguments
            if args:
                command += " " + " ".join(shlex.quote(arg) for arg in args)
            
            # Execute the script
            result = await self._execute_command(command)
            
            return AgentResult(
                success=result["success"],
                data=result,
                message=f"Script execution {'completed' if result['success'] else 'failed'}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to run script"
            )
