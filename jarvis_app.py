"""
Jarvis AI Assistant - Main Application
Core application logic and message processing
"""

import asyncio
import json
from datetime import datetime
from typing import Optional, Dict, Any, List
from core.config import config
from core.logger import get_logger
from core.memory import memory_manager
from core.llm import llm_manager
from agents.agent_dispatcher import dispatcher
from skills.skill_manager import skill_manager

logger = get_logger("jarvis_app")


class JarvisApp:
    """Main Jarvis AI Assistant application"""
    
    def __init__(self):
        self.initialized = False
        self.session_id = None
        self.conversation_history: List[Dict[str, Any]] = []
        self.user_context: Dict[str, Any] = {}
        
    async def initialize(self):
        """Initialize the <PERSON> application"""
        try:
            logger.info("Initializing Jarvis application...")
            
            # Generate session ID
            self.session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Initialize components (they're already initialized via imports)
            logger.info("✓ Core components loaded")
            logger.info("✓ Agents initialized")
            logger.info("✓ Skills loaded")
            logger.info("✓ Memory system ready")
            logger.info("✓ LLM providers ready")
            
            self.initialized = True
            logger.info("Jarvis application initialized successfully")
            
            # Store initialization in memory
            await memory_manager.store_memory(
                content=f"Jarvis session started: {self.session_id}",
                metadata={"session_id": self.session_id, "event": "initialization"},
                memory_type="system",
                importance=0.6
            )
            
        except Exception as e:
            logger.error(f"Jarvis initialization failed: {e}")
            raise
    
    async def process_message(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Process a user message and return response"""
        try:
            if not self.initialized:
                return "Jarvis is not initialized. Please restart the application."
            
            if not message or not message.strip():
                return "I didn't receive any message. How can I help you?"
            
            message = message.strip()
            logger.info(f"Processing message: {message}")
            
            # Update user context
            if context:
                self.user_context.update(context)
            
            # Add to conversation history
            self.conversation_history.append({
                "role": "user",
                "content": message,
                "timestamp": datetime.now().isoformat(),
                "session_id": self.session_id
            })
            
            # Analyze message intent
            intent_analysis = await self._analyze_intent(message)
            
            # Determine processing strategy
            strategy = await self._determine_strategy(message, intent_analysis)
            
            # Process based on strategy
            if strategy == "direct_llm":
                response = await self._process_with_llm(message, intent_analysis)
            elif strategy == "agent_dispatch":
                response = await self._process_with_agents(message, intent_analysis)
            elif strategy == "skill_execution":
                response = await self._process_with_skills(message, intent_analysis)
            elif strategy == "planning":
                response = await self._process_with_planning(message, intent_analysis)
            else:
                response = await self._process_general(message, intent_analysis)
            
            # Add response to conversation history
            self.conversation_history.append({
                "role": "assistant",
                "content": response,
                "timestamp": datetime.now().isoformat(),
                "session_id": self.session_id,
                "strategy": strategy
            })
            
            # Store conversation turn in memory
            await memory_manager.store_conversation_turn(
                user_message=message,
                assistant_response=response,
                metadata={
                    "session_id": self.session_id,
                    "strategy": strategy,
                    "intent": intent_analysis.get("intent", "unknown")
                }
            )
            
            # Limit conversation history
            if len(self.conversation_history) > 20:
                self.conversation_history = self.conversation_history[-10:]
            
            logger.info(f"Response generated using strategy: {strategy}")
            return response
            
        except Exception as e:
            logger.error(f"Message processing failed: {e}")
            return f"I encountered an error while processing your request: {str(e)}"
    
    async def _analyze_intent(self, message: str) -> Dict[str, Any]:
        """Analyze user message intent"""
        try:
            # Get relevant memories for context
            memories = await memory_manager.search_memories(
                query=message,
                limit=3,
                min_similarity=0.7
            )
            
            memory_context = ""
            if memories:
                memory_context = "\n".join([
                    f"- {memory[0].content[:100]}..." 
                    for memory in memories
                ])
            
            intent_prompt = f"""
            Analyze the intent and requirements of this user message:
            
            Message: "{message}"
            
            Recent context:
            {memory_context}
            
            Determine:
            1. Primary intent (question, request, command, conversation, etc.)
            2. Complexity level (simple, moderate, complex)
            3. Required capabilities (research, coding, system control, calculation, etc.)
            4. Urgency level (low, medium, high)
            5. Whether it requires multiple steps or agents
            
            Respond with JSON:
            {{
                "intent": "primary intent",
                "complexity": "simple|moderate|complex",
                "capabilities": ["capability1", "capability2"],
                "urgency": "low|medium|high",
                "multi_step": true/false,
                "category": "general|technical|creative|informational|task"
            }}
            """
            
            response = await llm_manager.generate(
                prompt=intent_prompt,
                temperature=0.2
            )
            
            # Parse JSON response
            try:
                intent_analysis = json.loads(response)
            except json.JSONDecodeError:
                # Fallback analysis
                intent_analysis = {
                    "intent": "general_query",
                    "complexity": "moderate",
                    "capabilities": ["general"],
                    "urgency": "medium",
                    "multi_step": False,
                    "category": "general"
                }
            
            logger.debug(f"Intent analysis: {intent_analysis}")
            return intent_analysis
            
        except Exception as e:
            logger.error(f"Intent analysis failed: {e}")
            return {
                "intent": "unknown",
                "complexity": "moderate",
                "capabilities": ["general"],
                "urgency": "medium",
                "multi_step": False,
                "category": "general"
            }
    
    async def _determine_strategy(self, message: str, intent_analysis: Dict[str, Any]) -> str:
        """Determine the best processing strategy"""
        try:
            complexity = intent_analysis.get("complexity", "moderate")
            multi_step = intent_analysis.get("multi_step", False)
            capabilities = intent_analysis.get("capabilities", [])
            category = intent_analysis.get("category", "general")
            
            # Complex multi-step tasks need planning
            if complexity == "complex" and multi_step:
                return "planning"
            
            # Check if skills can handle it
            skill_recommendations = skill_manager.get_skill_recommendations(message)
            if skill_recommendations and skill_recommendations[0]["confidence"] > 0.7:
                return "skill_execution"
            
            # Check if agents can handle it
            agent_recommendations = await dispatcher.get_agent_recommendations(message)
            if agent_recommendations and agent_recommendations[0]["confidence"] > 0.6:
                return "agent_dispatch"
            
            # Technical or task-oriented messages
            if category in ["technical", "task"] or any(
                cap in ["coding", "system_control", "file_management", "research"] 
                for cap in capabilities
            ):
                return "agent_dispatch"
            
            # Simple informational queries
            if category == "informational" and complexity == "simple":
                return "direct_llm"
            
            # Default to general processing
            return "general"
            
        except Exception as e:
            logger.error(f"Strategy determination failed: {e}")
            return "general"
    
    async def _process_with_llm(self, message: str, intent_analysis: Dict[str, Any]) -> str:
        """Process message directly with LLM"""
        try:
            # Get conversation context
            context = await memory_manager.get_conversation_context(limit=5)
            
            context_text = ""
            if context:
                context_text = "\n".join([
                    f"{msg['role']}: {msg['content']}" 
                    for msg in context[-3:]  # Last 3 messages
                ])
            
            system_prompt = f"""
            You are Jarvis, an intelligent AI assistant. You are helpful, knowledgeable, and friendly.
            
            Recent conversation:
            {context_text}
            
            User context: {json.dumps(self.user_context)}
            
            Respond naturally and helpfully to the user's message.
            """
            
            response = await llm_manager.generate(
                prompt=message,
                system_prompt=system_prompt,
                temperature=0.7
            )
            
            return response
            
        except Exception as e:
            logger.error(f"LLM processing failed: {e}")
            return "I'm having trouble processing that request right now. Please try again."
    
    async def _process_with_agents(self, message: str, intent_analysis: Dict[str, Any]) -> str:
        """Process message using agents"""
        try:
            # Dispatch to appropriate agent
            result = await dispatcher.dispatch_task(
                task=message,
                context={
                    "intent_analysis": intent_analysis,
                    "user_context": self.user_context,
                    "session_id": self.session_id
                }
            )
            
            if result.success:
                return result.message
            else:
                return f"I encountered an issue: {result.error or 'Unknown error'}"
                
        except Exception as e:
            logger.error(f"Agent processing failed: {e}")
            return "I'm having trouble with that request. Please try rephrasing it."
    
    async def _process_with_skills(self, message: str, intent_analysis: Dict[str, Any]) -> str:
        """Process message using skills"""
        try:
            # Execute with skill manager
            result = await skill_manager.execute_skill(
                command=message,
                parameters={
                    "intent_analysis": intent_analysis,
                    "user_context": self.user_context,
                    "session_id": self.session_id
                }
            )
            
            if result.success:
                return result.message
            else:
                return f"I couldn't complete that task: {result.error or 'Unknown error'}"
                
        except Exception as e:
            logger.error(f"Skill processing failed: {e}")
            return "I'm having trouble with that skill. Please try a different approach."
    
    async def _process_with_planning(self, message: str, intent_analysis: Dict[str, Any]) -> str:
        """Process complex message with planning"""
        try:
            # Use planner agent to create a plan
            plan_result = await dispatcher.dispatch_task(
                task=f"Create a plan for: {message}",
                context={
                    "intent_analysis": intent_analysis,
                    "user_context": self.user_context,
                    "session_id": self.session_id
                },
                preferred_agent="planner"
            )
            
            if not plan_result.success:
                return f"I couldn't create a plan for that task: {plan_result.error}"
            
            plan = plan_result.data
            
            # Execute the plan
            execution_result = await dispatcher.execute_plan(
                plan=plan,
                context={
                    "user_context": self.user_context,
                    "session_id": self.session_id
                }
            )
            
            if execution_result.success:
                success_rate = execution_result.data.get("success_rate", 0)
                total_steps = execution_result.data.get("total_steps", 0)
                successful_steps = execution_result.data.get("successful_steps", 0)
                
                return f"I've completed your request with {successful_steps}/{total_steps} steps successful. {execution_result.message}"
            else:
                return f"I encountered issues executing the plan: {execution_result.error}"
                
        except Exception as e:
            logger.error(f"Planning processing failed: {e}")
            return "I'm having trouble planning that task. Let me try a simpler approach."
    
    async def _process_general(self, message: str, intent_analysis: Dict[str, Any]) -> str:
        """General message processing fallback"""
        try:
            # Try skills first, then agents, then LLM
            
            # Try skills
            skill_result = await skill_manager.execute_skill(message)
            if skill_result.success:
                return skill_result.message
            
            # Try agents
            agent_result = await dispatcher.dispatch_task(message)
            if agent_result.success:
                return agent_result.message
            
            # Fallback to LLM
            return await self._process_with_llm(message, intent_analysis)
            
        except Exception as e:
            logger.error(f"General processing failed: {e}")
            return "I'm not sure how to help with that. Could you please rephrase your request?"
    
    async def get_conversation_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation history"""
        return self.conversation_history[-limit:]
    
    async def clear_conversation_history(self):
        """Clear conversation history"""
        self.conversation_history.clear()
        logger.info("Conversation history cleared")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get system status information"""
        try:
            memory_stats = await memory_manager.get_memory_stats()
            agent_status = await dispatcher.get_agent_status()
            active_tasks = await dispatcher.get_active_tasks()
            skills = skill_manager.list_skills()
            
            return {
                "session_id": self.session_id,
                "initialized": self.initialized,
                "conversation_length": len(self.conversation_history),
                "memory_stats": memory_stats,
                "agents": {
                    "available": len(agent_status),
                    "active_tasks": active_tasks.get("task_count", 0)
                },
                "skills": {
                    "total": len(skills),
                    "enabled": sum(1 for skill in skills if skill.get("enabled", False))
                },
                "llm_providers": llm_manager.list_providers()
            }
            
        except Exception as e:
            logger.error(f"Failed to get system status: {e}")
            return {"error": str(e)}
    
    async def shutdown(self):
        """Shutdown the application"""
        try:
            logger.info("Shutting down Jarvis application...")
            
            # Store shutdown event
            if self.initialized:
                await memory_manager.store_memory(
                    content=f"Jarvis session ended: {self.session_id}",
                    metadata={"session_id": self.session_id, "event": "shutdown"},
                    memory_type="system",
                    importance=0.6
                )
            
            # Cleanup skills
            await skill_manager.cleanup_all_skills()
            
            self.initialized = False
            logger.info("Jarvis application shutdown complete")
            
        except Exception as e:
            logger.error(f"Shutdown error: {e}")
