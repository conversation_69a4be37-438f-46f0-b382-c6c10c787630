"""
Research Agent for Jarvis AI Assistant
Handles information gathering and research tasks
"""

import json
import re
from typing import Dict, List, Optional, Any
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from .base_agent import BaseAgent, AgentResult
from core.llm import llm_manager
from core.memory import memory_manager


class ResearchAgent(BaseAgent):
    """Agent responsible for research and information gathering"""
    
    def __init__(self):
        super().__init__(
            name="research",
            description="Gathers information, conducts research, and synthesizes knowledge"
        )
        self.capabilities = [
            "web_research",
            "information_synthesis",
            "fact_checking",
            "source_analysis",
            "report_generation",
            "knowledge_extraction"
        ]
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def can_handle(self, task: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if this is a research task"""
        research_keywords = [
            "research", "find", "search", "investigate", "analyze", "study",
            "gather", "collect", "information", "data", "facts", "report",
            "summarize", "explain", "what is", "how to", "why", "when"
        ]
        
        task_lower = task.lower()
        return any(keyword in task_lower for keyword in research_keywords)
    
    async def execute(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> AgentResult:
        """Execute research task"""
        try:
            self.logger.info(f"Executing research task: {task}")
            
            # Determine the type of research operation
            operation = await self._classify_operation(task, context)
            
            if operation == "web_search":
                result = await self._web_research(task, context)
            elif operation == "synthesis":
                result = await self._synthesize_information(task, context)
            elif operation == "fact_check":
                result = await self._fact_check(task, context)
            elif operation == "analysis":
                result = await self._analyze_sources(task, context)
            elif operation == "report":
                result = await self._generate_report(task, context)
            else:
                result = await self._general_research(task, context)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Research task failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="Research task execution failed"
            )
    
    async def _classify_operation(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Classify the type of research operation"""
        task_lower = task.lower()
        
        if any(word in task_lower for word in ["search", "find", "look up", "google"]):
            return "web_search"
        elif any(word in task_lower for word in ["synthesize", "combine", "merge", "compile"]):
            return "synthesis"
        elif any(word in task_lower for word in ["fact check", "verify", "validate", "confirm"]):
            return "fact_check"
        elif any(word in task_lower for word in ["analyze", "examine", "evaluate", "assess"]):
            return "analysis"
        elif any(word in task_lower for word in ["report", "summary", "document", "write up"]):
            return "report"
        else:
            return "general"
    
    async def _web_research(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Conduct web research"""
        try:
            # Extract search query
            query = context.get("query") if context else None
            if not query:
                query = await self._extract_search_query(task)
            
            if not query:
                return AgentResult(
                    success=False,
                    error="No search query specified",
                    message="Please specify what to research"
                )
            
            # Perform multiple searches for comprehensive results
            search_results = []
            
            # Search engines and sources
            sources = [
                {"name": "Google", "url": f"https://www.google.com/search?q={query}"},
                {"name": "DuckDuckGo", "url": f"https://duckduckgo.com/?q={query}"}
            ]
            
            for source in sources:
                try:
                    results = await self._search_engine(source["url"], source["name"])
                    search_results.extend(results)
                except Exception as e:
                    self.logger.warning(f"Search failed for {source['name']}: {e}")
            
            # Remove duplicates and limit results
            unique_results = self._deduplicate_results(search_results)[:10]
            
            # Extract content from top results
            detailed_results = []
            for result in unique_results[:5]:  # Limit to top 5 for detailed extraction
                try:
                    content = await self._extract_page_content(result["url"])
                    result["content"] = content
                    detailed_results.append(result)
                except Exception as e:
                    self.logger.warning(f"Content extraction failed for {result['url']}: {e}")
                    detailed_results.append(result)
            
            # Store research in memory
            await memory_manager.store_memory(
                content=f"Research query: {query}\nResults: {json.dumps(detailed_results, indent=2)}",
                metadata={"query": query, "type": "research"},
                memory_type="research",
                importance=0.8
            )
            
            return AgentResult(
                success=True,
                data={
                    "query": query,
                    "results": detailed_results,
                    "total_found": len(unique_results),
                    "sources_checked": len(sources)
                },
                message=f"Found {len(detailed_results)} detailed results for: {query}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to conduct web research"
            )
    
    async def _synthesize_information(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Synthesize information from multiple sources"""
        try:
            # Get sources from context or search for them
            sources = context.get("sources", []) if context else []
            
            if not sources:
                # Search for relevant information
                search_result = await self._web_research(task, context)
                if search_result.success:
                    sources = search_result.data.get("results", [])
            
            if not sources:
                return AgentResult(
                    success=False,
                    error="No sources available",
                    message="No sources found to synthesize information from"
                )
            
            # Prepare synthesis prompt
            source_texts = []
            for i, source in enumerate(sources[:5]):  # Limit to 5 sources
                content = source.get("content", source.get("snippet", ""))
                if content:
                    source_texts.append(f"Source {i+1} ({source.get('title', 'Unknown')}):\n{content}")
            
            synthesis_prompt = f"""
            Synthesize information from the following sources to answer this task:
            
            Task: {task}
            
            Sources:
            {chr(10).join(source_texts)}
            
            Please provide:
            1. A comprehensive synthesis of the information
            2. Key findings and insights
            3. Areas of agreement and disagreement between sources
            4. Gaps in information or areas needing further research
            5. Confidence level in the findings
            
            Format your response as a structured analysis.
            """
            
            synthesis = await llm_manager.generate(
                prompt=synthesis_prompt,
                temperature=0.3
            )
            
            # Store synthesis in memory
            await memory_manager.store_memory(
                content=f"Synthesis for: {task}\n{synthesis}",
                metadata={"task": task, "type": "synthesis", "source_count": len(sources)},
                memory_type="research",
                importance=0.9
            )
            
            return AgentResult(
                success=True,
                data={
                    "synthesis": synthesis,
                    "sources_used": len(sources),
                    "task": task
                },
                message=f"Synthesized information from {len(sources)} sources"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to synthesize information"
            )
    
    async def _fact_check(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Fact-check claims or statements"""
        try:
            claim = context.get("claim") if context else None
            
            if not claim:
                # Extract claim from task
                claim = await self._extract_claim(task)
            
            if not claim:
                return AgentResult(
                    success=False,
                    error="No claim specified",
                    message="Please specify a claim to fact-check"
                )
            
            # Search for evidence
            search_query = f"fact check {claim}"
            search_result = await self._web_research(search_query, {"query": search_query})
            
            if not search_result.success:
                return AgentResult(
                    success=False,
                    error="Search failed",
                    message="Failed to search for fact-checking information"
                )
            
            sources = search_result.data.get("results", [])
            
            # Analyze evidence
            fact_check_prompt = f"""
            Fact-check this claim using the provided sources:
            
            Claim: {claim}
            
            Sources:
            {chr(10).join([f"- {s.get('title', '')}: {s.get('snippet', '')}" for s in sources[:5]])}
            
            Please provide:
            1. Verdict: True, False, Partially True, or Insufficient Evidence
            2. Explanation of your reasoning
            3. Supporting evidence
            4. Contradicting evidence (if any)
            5. Confidence level (1-10)
            6. Recommendations for further verification
            
            Be objective and cite specific sources.
            """
            
            fact_check_result = await llm_manager.generate(
                prompt=fact_check_prompt,
                temperature=0.2
            )
            
            return AgentResult(
                success=True,
                data={
                    "claim": claim,
                    "fact_check": fact_check_result,
                    "sources_checked": len(sources),
                    "search_query": search_query
                },
                message=f"Fact-checked claim: {claim[:50]}..."
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to fact-check"
            )
    
    async def _analyze_sources(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Analyze sources for credibility and bias"""
        try:
            sources = context.get("sources", []) if context else []
            
            if not sources:
                return AgentResult(
                    success=False,
                    error="No sources provided",
                    message="Please provide sources to analyze"
                )
            
            analyses = []
            
            for source in sources:
                url = source.get("url", "")
                title = source.get("title", "")
                content = source.get("content", source.get("snippet", ""))
                
                analysis_prompt = f"""
                Analyze this source for credibility and potential bias:
                
                URL: {url}
                Title: {title}
                Content: {content[:1000]}...
                
                Please evaluate:
                1. Source credibility (1-10)
                2. Potential bias (left, right, center, unknown)
                3. Content quality (1-10)
                4. Factual accuracy indicators
                5. Red flags or concerns
                6. Overall trustworthiness assessment
                
                Provide a brief analysis with specific examples.
                """
                
                try:
                    analysis = await llm_manager.generate(
                        prompt=analysis_prompt,
                        temperature=0.3
                    )
                    
                    analyses.append({
                        "source": source,
                        "analysis": analysis
                    })
                except Exception as e:
                    self.logger.warning(f"Analysis failed for source {url}: {e}")
            
            return AgentResult(
                success=True,
                data={
                    "analyses": analyses,
                    "sources_analyzed": len(analyses),
                    "task": task
                },
                message=f"Analyzed {len(analyses)} sources for credibility and bias"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to analyze sources"
            )
    
    async def _generate_report(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Generate a research report"""
        try:
            topic = context.get("topic") if context else None
            
            if not topic:
                topic = await self._extract_topic(task)
            
            if not topic:
                return AgentResult(
                    success=False,
                    error="No topic specified",
                    message="Please specify a topic for the report"
                )
            
            # Gather information
            research_result = await self._web_research(f"research {topic}", {"query": topic})
            
            if not research_result.success:
                return AgentResult(
                    success=False,
                    error="Research failed",
                    message="Failed to gather information for the report"
                )
            
            sources = research_result.data.get("results", [])
            
            # Generate report
            report_prompt = f"""
            Generate a comprehensive research report on the following topic:
            
            Topic: {topic}
            
            Based on these sources:
            {chr(10).join([f"- {s.get('title', '')}: {s.get('snippet', '')}" for s in sources[:10]])}
            
            Structure the report with:
            1. Executive Summary
            2. Introduction
            3. Key Findings
            4. Detailed Analysis
            5. Conclusions
            6. Recommendations
            7. Sources and References
            
            Make it professional and well-structured.
            """
            
            report = await llm_manager.generate(
                prompt=report_prompt,
                temperature=0.4
            )
            
            # Store report in memory
            await memory_manager.store_memory(
                content=f"Research Report: {topic}\n{report}",
                metadata={"topic": topic, "type": "report", "source_count": len(sources)},
                memory_type="research",
                importance=1.0
            )
            
            return AgentResult(
                success=True,
                data={
                    "topic": topic,
                    "report": report,
                    "sources_used": len(sources),
                    "word_count": len(report.split())
                },
                message=f"Generated research report on: {topic}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to generate report"
            )
    
    async def _general_research(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Handle general research tasks"""
        try:
            # Check memory for relevant past research
            memories = await memory_manager.search_memories(
                query=task,
                memory_type="research",
                limit=5
            )
            
            memory_context = ""
            if memories:
                memory_context = "\n".join([
                    f"Past research: {memory[0].content[:200]}..." 
                    for memory in memories
                ])
            
            # Conduct new research
            research_result = await self._web_research(task, context)
            
            if research_result.success:
                sources = research_result.data.get("results", [])
                
                # Generate comprehensive answer
                answer_prompt = f"""
                Answer this research question comprehensively:
                
                Question: {task}
                
                Recent research findings:
                {chr(10).join([f"- {s.get('title', '')}: {s.get('snippet', '')}" for s in sources[:5]])}
                
                Relevant past research:
                {memory_context}
                
                Provide a thorough, well-researched answer with:
                1. Direct answer to the question
                2. Supporting evidence
                3. Multiple perspectives if applicable
                4. Limitations or uncertainties
                5. Suggestions for further research
                """
                
                answer = await llm_manager.generate(
                    prompt=answer_prompt,
                    temperature=0.4
                )
                
                return AgentResult(
                    success=True,
                    data={
                        "question": task,
                        "answer": answer,
                        "sources": sources,
                        "memory_references": len(memories)
                    },
                    message=f"Researched and answered: {task[:50]}..."
                )
            else:
                return research_result
                
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to conduct general research"
            )
    
    async def _search_engine(self, url: str, engine_name: str) -> List[Dict[str, Any]]:
        """Search using a specific search engine"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            results = []
            
            if "google" in url.lower():
                # Parse Google results
                for result_div in soup.find_all('div', class_='g'):
                    title_elem = result_div.find('h3')
                    link_elem = result_div.find('a')
                    snippet_elem = result_div.find('span', class_='aCOpRe')
                    
                    if title_elem and link_elem:
                        results.append({
                            "title": title_elem.get_text(strip=True),
                            "url": link_elem.get('href', ''),
                            "snippet": snippet_elem.get_text(strip=True) if snippet_elem else "",
                            "source": engine_name
                        })
            
            elif "duckduckgo" in url.lower():
                # Parse DuckDuckGo results
                for result_div in soup.find_all('div', class_='result'):
                    title_elem = result_div.find('a', class_='result__a')
                    snippet_elem = result_div.find('a', class_='result__snippet')
                    
                    if title_elem:
                        results.append({
                            "title": title_elem.get_text(strip=True),
                            "url": title_elem.get('href', ''),
                            "snippet": snippet_elem.get_text(strip=True) if snippet_elem else "",
                            "source": engine_name
                        })
            
            return results
            
        except Exception as e:
            self.logger.error(f"Search engine query failed for {engine_name}: {e}")
            return []
    
    async def _extract_page_content(self, url: str) -> str:
        """Extract main content from a web page"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text content
            text = soup.get_text()
            
            # Clean up text
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            # Limit length
            return text[:2000] + "..." if len(text) > 2000 else text
            
        except Exception as e:
            self.logger.warning(f"Content extraction failed for {url}: {e}")
            return ""
    
    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate results based on URL and title"""
        seen_urls = set()
        seen_titles = set()
        unique_results = []
        
        for result in results:
            url = result.get("url", "")
            title = result.get("title", "")
            
            if url not in seen_urls and title not in seen_titles:
                seen_urls.add(url)
                seen_titles.add(title)
                unique_results.append(result)
        
        return unique_results
    
    async def _extract_search_query(self, task: str) -> str:
        """Extract search query from task"""
        # Remove common prefixes
        query = task.lower()
        prefixes = [
            "research", "find", "search for", "look up", "investigate",
            "what is", "how to", "why", "when", "where", "who"
        ]
        
        for prefix in prefixes:
            if query.startswith(prefix):
                query = query[len(prefix):].strip()
                break
        
        return query
    
    async def _extract_claim(self, task: str) -> str:
        """Extract claim to fact-check from task"""
        # Look for quoted claims
        import re
        quoted_matches = re.findall(r'["\']([^"\']+)["\']', task)
        if quoted_matches:
            return quoted_matches[0]
        
        # Remove fact-check prefixes
        claim = task.lower()
        prefixes = ["fact check", "verify", "is it true that", "check if"]
        
        for prefix in prefixes:
            if claim.startswith(prefix):
                claim = claim[len(prefix):].strip()
                break
        
        return claim
    
    async def _extract_topic(self, task: str) -> str:
        """Extract topic for report from task"""
        # Remove report-related prefixes
        topic = task.lower()
        prefixes = [
            "write a report on", "generate report about", "report on",
            "research report on", "create report for"
        ]
        
        for prefix in prefixes:
            if topic.startswith(prefix):
                topic = topic[len(prefix):].strip()
                break
        
        return topic
