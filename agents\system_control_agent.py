"""
System Control Agent for Jarvis AI Assistant
Handles system-level operations and PC control
"""

import os
import psutil
import platform
import subprocess
import time
from typing import Dict, List, Optional, Any
import pyautogui
import keyboard
from .base_agent import BaseAgent, AgentResult
from core.config import config
from core.llm import llm_manager


class SystemControlAgent(BaseAgent):
    """Agent responsible for system control and PC automation"""
    
    def __init__(self):
        super().__init__(
            name="system_control",
            description="Controls system operations, applications, and PC automation"
        )
        self.capabilities = [
            "process_management",
            "application_control",
            "mouse_automation",
            "keyboard_automation",
            "window_management",
            "system_monitoring",
            "power_management"
        ]
        
        # Configure pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
    
    def can_handle(self, task: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if this is a system control task"""
        system_keywords = [
            "open", "close", "start", "stop", "kill", "process", "application",
            "click", "type", "keyboard", "mouse", "window", "minimize", "maximize",
            "volume", "brightness", "power", "shutdown", "restart", "sleep",
            "monitor", "system", "performance", "memory", "cpu", "disk"
        ]
        
        task_lower = task.lower()
        return any(keyword in task_lower for keyword in system_keywords)
    
    async def execute(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> AgentResult:
        """Execute system control task"""
        try:
            self.logger.info(f"Executing system control task: {task}")
            
            # Determine the type of system operation
            operation = await self._classify_operation(task, context)
            
            if operation == "process":
                result = await self._manage_process(task, context)
            elif operation == "application":
                result = await self._control_application(task, context)
            elif operation == "mouse":
                result = await self._mouse_automation(task, context)
            elif operation == "keyboard":
                result = await self._keyboard_automation(task, context)
            elif operation == "window":
                result = await self._window_management(task, context)
            elif operation == "monitor":
                result = await self._system_monitoring(task, context)
            elif operation == "power":
                result = await self._power_management(task, context)
            else:
                result = await self._general_system_operation(task, context)
            
            return result
            
        except Exception as e:
            self.logger.error(f"System control task failed: {e}")
            return AgentResult(
                success=False,
                error=str(e),
                message="System control task execution failed"
            )
    
    async def _classify_operation(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Classify the type of system operation"""
        task_lower = task.lower()
        
        if any(word in task_lower for word in ["process", "kill", "terminate", "pid"]):
            return "process"
        elif any(word in task_lower for word in ["open", "start", "launch", "run", "close", "quit"]):
            return "application"
        elif any(word in task_lower for word in ["click", "mouse", "cursor", "drag"]):
            return "mouse"
        elif any(word in task_lower for word in ["type", "keyboard", "key", "shortcut"]):
            return "keyboard"
        elif any(word in task_lower for word in ["window", "minimize", "maximize", "resize"]):
            return "window"
        elif any(word in task_lower for word in ["monitor", "performance", "cpu", "memory", "disk"]):
            return "monitor"
        elif any(word in task_lower for word in ["shutdown", "restart", "sleep", "power"]):
            return "power"
        else:
            return "general"
    
    async def _manage_process(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Manage system processes"""
        try:
            action = "list"  # Default action
            process_name = None
            
            task_lower = task.lower()
            
            if "kill" in task_lower or "terminate" in task_lower:
                action = "kill"
            elif "start" in task_lower or "run" in task_lower:
                action = "start"
            elif "list" in task_lower or "show" in task_lower:
                action = "list"
            
            # Extract process name
            if context and "process_name" in context:
                process_name = context["process_name"]
            else:
                process_name = self._extract_process_name(task)
            
            if action == "list":
                # List running processes
                processes = []
                for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                    try:
                        proc_info = proc.info
                        if process_name is None or process_name.lower() in proc_info['name'].lower():
                            processes.append(proc_info)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                
                # Sort by CPU usage
                processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
                
                return AgentResult(
                    success=True,
                    data={
                        "processes": processes[:20],  # Limit to top 20
                        "total_count": len(processes),
                        "filter": process_name
                    },
                    message=f"Found {len(processes)} processes" + (f" matching '{process_name}'" if process_name else "")
                )
            
            elif action == "kill":
                if not process_name:
                    return AgentResult(
                        success=False,
                        error="No process name specified",
                        message="Please specify a process name to kill"
                    )
                
                killed_count = 0
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        if process_name.lower() in proc.info['name'].lower():
                            proc.terminate()
                            killed_count += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                
                return AgentResult(
                    success=killed_count > 0,
                    data={"killed_count": killed_count, "process_name": process_name},
                    message=f"Terminated {killed_count} processes matching '{process_name}'"
                )
            
            elif action == "start":
                if not process_name:
                    return AgentResult(
                        success=False,
                        error="No process name specified",
                        message="Please specify a process/application to start"
                    )
                
                # Try to start the process
                try:
                    if platform.system() == "Windows":
                        subprocess.Popen(process_name, shell=True)
                    else:
                        subprocess.Popen(process_name, shell=True)
                    
                    return AgentResult(
                        success=True,
                        data={"process_name": process_name},
                        message=f"Started process: {process_name}"
                    )
                except Exception as e:
                    return AgentResult(
                        success=False,
                        error=str(e),
                        message=f"Failed to start process: {process_name}"
                    )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to manage process"
            )
    
    async def _control_application(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Control applications"""
        try:
            app_name = context.get("application") if context else None
            
            if not app_name:
                app_name = self._extract_application_name(task)
            
            if not app_name:
                return AgentResult(
                    success=False,
                    error="No application specified",
                    message="Please specify an application name"
                )
            
            task_lower = task.lower()
            
            if any(word in task_lower for word in ["open", "start", "launch", "run"]):
                # Open application
                try:
                    if platform.system() == "Windows":
                        os.startfile(app_name)
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.run(["open", "-a", app_name])
                    else:  # Linux
                        subprocess.run([app_name])
                    
                    return AgentResult(
                        success=True,
                        data={"application": app_name, "action": "open"},
                        message=f"Opened application: {app_name}"
                    )
                except Exception as e:
                    return AgentResult(
                        success=False,
                        error=str(e),
                        message=f"Failed to open application: {app_name}"
                    )
            
            elif any(word in task_lower for word in ["close", "quit", "exit"]):
                # Close application by killing its processes
                killed_count = 0
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        if app_name.lower() in proc.info['name'].lower():
                            proc.terminate()
                            killed_count += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                
                return AgentResult(
                    success=killed_count > 0,
                    data={"application": app_name, "action": "close", "processes_killed": killed_count},
                    message=f"Closed application: {app_name} ({killed_count} processes)"
                )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to control application"
            )
    
    async def _mouse_automation(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Perform mouse automation"""
        try:
            task_lower = task.lower()
            
            if "click" in task_lower:
                # Extract coordinates or use current position
                x = context.get("x") if context else None
                y = context.get("y") if context else None
                
                if x is None or y is None:
                    # Use current mouse position
                    x, y = pyautogui.position()
                
                # Determine click type
                if "right" in task_lower:
                    pyautogui.rightClick(x, y)
                    click_type = "right"
                elif "double" in task_lower:
                    pyautogui.doubleClick(x, y)
                    click_type = "double"
                else:
                    pyautogui.click(x, y)
                    click_type = "left"
                
                return AgentResult(
                    success=True,
                    data={"action": "click", "type": click_type, "x": x, "y": y},
                    message=f"Performed {click_type} click at ({x}, {y})"
                )
            
            elif "move" in task_lower:
                x = context.get("x", 100) if context else 100
                y = context.get("y", 100) if context else 100
                
                pyautogui.moveTo(x, y)
                
                return AgentResult(
                    success=True,
                    data={"action": "move", "x": x, "y": y},
                    message=f"Moved mouse to ({x}, {y})"
                )
            
            elif "scroll" in task_lower:
                clicks = context.get("clicks", 3) if context else 3
                
                if "up" in task_lower:
                    pyautogui.scroll(clicks)
                    direction = "up"
                else:
                    pyautogui.scroll(-clicks)
                    direction = "down"
                
                return AgentResult(
                    success=True,
                    data={"action": "scroll", "direction": direction, "clicks": clicks},
                    message=f"Scrolled {direction} {clicks} clicks"
                )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to perform mouse automation"
            )
    
    async def _keyboard_automation(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Perform keyboard automation"""
        try:
            task_lower = task.lower()
            
            if "type" in task_lower:
                text = context.get("text") if context else None
                
                if not text:
                    # Extract text from task
                    text = self._extract_text_to_type(task)
                
                if not text:
                    return AgentResult(
                        success=False,
                        error="No text specified",
                        message="Please specify text to type"
                    )
                
                pyautogui.typewrite(text)
                
                return AgentResult(
                    success=True,
                    data={"action": "type", "text": text},
                    message=f"Typed text: {text[:50]}{'...' if len(text) > 50 else ''}"
                )
            
            elif "key" in task_lower or "shortcut" in task_lower:
                keys = context.get("keys") if context else None
                
                if not keys:
                    keys = self._extract_key_combination(task)
                
                if not keys:
                    return AgentResult(
                        success=False,
                        error="No keys specified",
                        message="Please specify keys to press"
                    )
                
                if isinstance(keys, list):
                    pyautogui.hotkey(*keys)
                else:
                    pyautogui.press(keys)
                
                return AgentResult(
                    success=True,
                    data={"action": "key", "keys": keys},
                    message=f"Pressed keys: {keys}"
                )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to perform keyboard automation"
            )
    
    async def _window_management(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Manage windows"""
        try:
            task_lower = task.lower()
            
            if "minimize" in task_lower:
                pyautogui.hotkey('alt', 'f9')  # Windows minimize shortcut
                action = "minimize"
            elif "maximize" in task_lower:
                pyautogui.hotkey('alt', 'f10')  # Windows maximize shortcut
                action = "maximize"
            elif "close" in task_lower:
                pyautogui.hotkey('alt', 'f4')  # Windows close shortcut
                action = "close"
            else:
                return AgentResult(
                    success=False,
                    error="Unknown window action",
                    message="Supported actions: minimize, maximize, close"
                )
            
            return AgentResult(
                success=True,
                data={"action": action},
                message=f"Performed window action: {action}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to manage window"
            )
    
    async def _system_monitoring(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Monitor system performance"""
        try:
            # Get system information
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Get network info
            network = psutil.net_io_counters()
            
            # Get running processes count
            process_count = len(psutil.pids())
            
            system_info = {
                "cpu": {
                    "percent": cpu_percent,
                    "count": psutil.cpu_count(),
                    "freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent,
                    "used": memory.used
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                },
                "processes": process_count,
                "uptime": time.time() - psutil.boot_time()
            }
            
            return AgentResult(
                success=True,
                data=system_info,
                message="Retrieved system performance information"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to monitor system"
            )
    
    async def _power_management(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Manage system power"""
        try:
            task_lower = task.lower()
            
            if not config.system.allow_system_commands:
                return AgentResult(
                    success=False,
                    error="System commands disabled",
                    message="Power management commands are disabled for safety"
                )
            
            if "shutdown" in task_lower:
                if platform.system() == "Windows":
                    subprocess.run(["shutdown", "/s", "/t", "10"])
                else:
                    subprocess.run(["sudo", "shutdown", "-h", "+1"])
                action = "shutdown"
            elif "restart" in task_lower:
                if platform.system() == "Windows":
                    subprocess.run(["shutdown", "/r", "/t", "10"])
                else:
                    subprocess.run(["sudo", "reboot"])
                action = "restart"
            elif "sleep" in task_lower:
                if platform.system() == "Windows":
                    subprocess.run(["rundll32.exe", "powrprof.dll,SetSuspendState", "0,1,0"])
                else:
                    subprocess.run(["sudo", "systemctl", "suspend"])
                action = "sleep"
            else:
                return AgentResult(
                    success=False,
                    error="Unknown power action",
                    message="Supported actions: shutdown, restart, sleep"
                )
            
            return AgentResult(
                success=True,
                data={"action": action},
                message=f"Initiated {action} command"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to manage power"
            )
    
    async def _general_system_operation(
        self,
        task: str,
        context: Optional[Dict[str, Any]] = None
    ) -> AgentResult:
        """Handle general system operations"""
        general_prompt = f"""
        You are a system administration expert. Help with this system control task:
        
        Task: {task}
        Context: {context or {}}
        Operating System: {platform.system()}
        
        Provide specific instructions for the system operation, including:
        1. What operation needs to be performed
        2. Required parameters or commands
        3. Step-by-step instructions
        4. Safety considerations
        """
        
        try:
            response = await llm_manager.generate(
                prompt=general_prompt,
                temperature=0.3
            )
            
            return AgentResult(
                success=True,
                data={"instructions": response},
                message="Generated system operation instructions"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error=str(e),
                message="Failed to process general system operation"
            )
    
    def _extract_process_name(self, task: str) -> Optional[str]:
        """Extract process name from task"""
        # Simple extraction - look for common process names
        common_processes = [
            "chrome", "firefox", "notepad", "calculator", "explorer",
            "python", "java", "node", "code", "discord", "spotify"
        ]
        
        task_lower = task.lower()
        for process in common_processes:
            if process in task_lower:
                return process
        
        return None
    
    def _extract_application_name(self, task: str) -> Optional[str]:
        """Extract application name from task"""
        # Common application names and their executables
        app_mapping = {
            "notepad": "notepad.exe",
            "calculator": "calc.exe",
            "chrome": "chrome.exe",
            "firefox": "firefox.exe",
            "explorer": "explorer.exe",
            "word": "winword.exe",
            "excel": "excel.exe",
            "powerpoint": "powerpnt.exe"
        }
        
        task_lower = task.lower()
        for app_name, executable in app_mapping.items():
            if app_name in task_lower:
                return executable
        
        # Look for quoted application names
        import re
        quoted_matches = re.findall(r'["\']([^"\']+)["\']', task)
        if quoted_matches:
            return quoted_matches[0]
        
        return None
    
    def _extract_text_to_type(self, task: str) -> Optional[str]:
        """Extract text to type from task"""
        import re
        
        # Look for quoted text
        quoted_matches = re.findall(r'["\']([^"\']+)["\']', task)
        if quoted_matches:
            return quoted_matches[0]
        
        # Look for "type" followed by text
        type_matches = re.findall(r'type\s+(.+)', task, re.IGNORECASE)
        if type_matches:
            return type_matches[0].strip()
        
        return None
    
    def _extract_key_combination(self, task: str) -> Optional[str]:
        """Extract key combination from task"""
        # Common key combinations
        key_mappings = {
            "ctrl+c": ["ctrl", "c"],
            "ctrl+v": ["ctrl", "v"],
            "ctrl+z": ["ctrl", "z"],
            "alt+tab": ["alt", "tab"],
            "alt+f4": ["alt", "f4"],
            "win+r": ["win", "r"],
            "enter": "enter",
            "escape": "esc",
            "space": "space"
        }
        
        task_lower = task.lower()
        for key_combo, keys in key_mappings.items():
            if key_combo in task_lower:
                return keys
        
        return None
